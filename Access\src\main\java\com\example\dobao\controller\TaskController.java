package com.example.dobao.controller;

import com.example.dobao.entity.TaskInfo;
import com.example.dobao.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.io.FileOutputStream;
import java.io.FileInputStream;
import java.io.ByteArrayOutputStream;

/**
 * 任务管理控制器
 */
@RestController
@RequestMapping("/api/tasks")
@Slf4j
public class TaskController {

    @javax.annotation.Resource
    private TaskService taskService;

    /**
     * 导入Excel文件
     */
    @PostMapping("/import")
    public ResponseEntity<Map<String, Object>> importExcel(@RequestParam("file") MultipartFile file, 
                                                           @RequestParam(value = "batchName", required = false) String batchName) {
        try {
            String batchNo = taskService.importTasksFromExcel(file, batchName);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "导入成功");
            result.put("data", batchNo);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("导入Excel失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "导入失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 开始处理任务
     */
    @PostMapping("/process/{batchNo}")
    public ResponseEntity<Map<String, Object>> processTask(@PathVariable String batchNo) {
        try {
            taskService.startProcessTasks(batchNo);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "任务处理已启动");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("启动任务处理失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "启动任务处理失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取处理进度
     */
    @GetMapping("/progress/{batchNo}")
    public ResponseEntity<Map<String, Object>> getProcessProgress(@PathVariable String batchNo) {
        try {
            Map<String, Object> progress = taskService.getProcessProgress(batchNo);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "获取进度成功");
            result.put("data", progress);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取处理进度失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "获取处理进度失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取批次任务列表
     */
    @GetMapping("/list/{batchNo}")
    public ResponseEntity<Map<String, Object>> getTasksByBatchNo(@PathVariable String batchNo) {
        try {
            List<TaskInfo> tasks = taskService.getTasksByBatchNo(batchNo);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "获取成功");
            result.put("data", tasks);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "获取任务列表失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取所有批次
     */
    @GetMapping("/batches")
    public ResponseEntity<Map<String, Object>> getAllBatches() {
        try {
            List<String> batches = taskService.getAllBatchNumbers();
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "获取成功");
            result.put("data", batches);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取批次列表失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "获取批次列表失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 下载Word文件
     */
    @GetMapping("/download/{id}")
    public ResponseEntity<org.springframework.core.io.Resource> downloadWord(@PathVariable Long id) {
        try {
            String filePath = taskService.getWordFilePath(id);
            if (filePath == null) {
                return ResponseEntity.notFound().build();
            }
            
            File file = new File(filePath);
            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            Path path = Paths.get(filePath);
            String contentType = Files.probeContentType(path);
            
            // 获取任务信息
            TaskInfo taskInfo = taskService.getTaskById(id);
            
            // 设置文件名
            String fileName = taskInfo.getFileName();
            if (!fileName.toLowerCase().endsWith(".docx")) {
                fileName = fileName + ".docx";
            }
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                    .body(new FileSystemResource(file));
        } catch (Exception e) {
            log.error("下载Word文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 删除Word文件
     */
    @DeleteMapping("/deleteWord/{id}")
    public ResponseEntity<Map<String, Object>> deleteWord(@PathVariable Long id) {
        try {
            // 获取Word文件路径
            String filePath = taskService.getWordFilePath(id);
            if (filePath == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("code", 1);
                result.put("message", "文件不存在");
                return ResponseEntity.ok(result);
            }
            
            // 删除文件
            File file = new File(filePath);
            if (file.exists() && file.isFile()) {
                boolean deleted = file.delete();
                if (!deleted) {
                    throw new IOException("无法删除文件: " + filePath);
                }
            }
            
            // 记录文件已删除
            taskService.markWordFileDeleted(id);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "删除成功");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除Word文件失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 物理删除Word文件
     */
    @DeleteMapping("/physicalDeleteWord/{id}")
    public ResponseEntity<Map<String, Object>> physicalDeleteWord(@PathVariable Long id) {
        try {
            boolean success = taskService.physicalDeleteTask(id);
            
            Map<String, Object> result = new HashMap<>();
            if (success) {
                result.put("code", 0);
                result.put("message", "文件和数据库记录已彻底删除");
            } else {
                result.put("code", 1);
                result.put("message", "删除失败");
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("物理删除Word文件和数据库记录失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 批量删除Word文件
     */
    @DeleteMapping("/batchDeleteWords")
    public ResponseEntity<Map<String, Object>> batchDeleteWords(@RequestBody List<Long> ids) {
        try {
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();
            
            for (Long id : ids) {
                try {
                    // 获取Word文件路径
                    String filePath = taskService.getWordFilePath(id);
                    if (filePath == null) {
                        failCount++;
                        errorMessages.append("ID ").append(id).append(": 文件不存在\n");
                        continue;
                    }
                    
                    // 删除文件
                    File file = new File(filePath);
                    if (file.exists() && file.isFile()) {
                        boolean deleted = file.delete();
                        if (!deleted) {
                            failCount++;
                            errorMessages.append("ID ").append(id).append(": 文件删除失败\n");
                            continue;
                        }
                    }
                    
                    // 记录文件已删除
                    taskService.markWordFileDeleted(id);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    errorMessages.append("ID ").append(id).append(": ").append(e.getMessage()).append("\n");
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "批量删除完成: " + successCount + " 个成功, " + failCount + " 个失败");
            if (failCount > 0) {
                result.put("errorDetail", errorMessages.toString());
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量删除Word文件失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "批量删除失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 批量下载Word文件（打包为ZIP，按photoname分组）
     */
    @PostMapping("/batchDownloadWords")
    public ResponseEntity<byte[]> batchDownloadWords(@RequestBody List<Long> ids) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (ZipOutputStream zos = new ZipOutputStream(baos)) {
            int successCount = 0;
            
            // 先按photoname分组
            Map<String, List<TaskInfo>> groupedTasks = new HashMap<>();
            
            for (Long id : ids) {
                try {
                    // 获取任务信息
                    TaskInfo taskInfo = taskService.getTaskById(id);
                    if (taskInfo == null || taskInfo.getStatus() != 2) {
                        continue;
                    }
                    
                    // 获取Word文件路径
                    String filePath = taskService.getWordFilePath(id);
                    if (filePath == null) {
                        continue;
                    }
                    
                    File file = new File(filePath);
                    if (!file.exists() || !file.isFile()) {
                        continue;
                    }
                    
                    // 按photoname分组，如果为空则放入"root"组
                    String groupKey = (taskInfo.getPhotoName() == null || taskInfo.getPhotoName().trim().isEmpty()) ? "root" : taskInfo.getPhotoName();
                    
                    if (!groupedTasks.containsKey(groupKey)) {
                        groupedTasks.put(groupKey, new ArrayList<>());
                    }
                    groupedTasks.get(groupKey).add(taskInfo);
                } catch (Exception e) {
                    log.error("处理任务ID={}时出错", id, e);
                }
            }
            
            // 遍历分组处理
            for (Map.Entry<String, List<TaskInfo>> entry : groupedTasks.entrySet()) {
                String groupKey = entry.getKey();
                List<TaskInfo> tasks = entry.getValue();
                
                for (TaskInfo taskInfo : tasks) {
                    try {
                        // 获取Word文件路径
                        String filePath = taskService.getWordFilePath(taskInfo.getId());
                        if (filePath == null) {
                            continue;
                        }
                        
                        File file = new File(filePath);
                        if (!file.exists() || !file.isFile()) {
                            continue;
                        }
                        
                        // 设置文件名
                        String fileName = taskInfo.getFileName();
                        if (!fileName.toLowerCase().endsWith(".docx")) {
                            fileName = fileName + ".docx";
                        }
                        
                        // 根据分组创建ZIP路径
                        String zipEntryPath;
                        if ("root".equals(groupKey)) {
                            // 根目录文件
                            zipEntryPath = fileName;
                        } else {
                            // 按photoname分组
                            zipEntryPath = groupKey + "/" + fileName;
                        }
                        // 添加到ZIP
                        try (FileInputStream fis = new FileInputStream(file)) {
                            ZipEntry zipEntry = new ZipEntry(zipEntryPath);
                            zos.putNextEntry(zipEntry);
                            
                            byte[] buffer = new byte[1024];
                            int len;
                            while ((len = fis.read(buffer)) > 0) {
                                zos.write(buffer, 0, len);
                            }
                            
                            zos.closeEntry();
                            successCount++;
                        }
                    } catch (Exception e) {
                        log.error("添加文件到ZIP包失败: ID=" + taskInfo.getId(), e);
                    }
                }
            }
            
            // 如果没有成功添加任何文件，返回404
            if (successCount == 0) {
                return ResponseEntity.notFound().build();
            }
            
            zos.finish();
            zos.close();
            
            // 生成压缩包名称: photoName+目录内文件数量+'篇'
            System.out.printf("zipEntryPath");

            StringBuilder zipFileName = new StringBuilder();
            int groupCount = 0;
            for (Map.Entry<String, List<TaskInfo>> entry : groupedTasks.entrySet()) {
                String groupKey = entry.getKey();
                List<TaskInfo> tasks = entry.getValue();
                
                // 跳过根目录
                if (!"root".equals(groupKey)) {
                    if (groupCount > 0) {
                        zipFileName.append("_");
                    }
                    zipFileName.append(groupKey).append(tasks.size()).append("篇");
                    groupCount++;
                    
                    // 如果分组太多，截断名称
                    if (groupCount >= 3) {
                        zipFileName.append("等");
                        break;
                    }
                }
            }
            
            // 如果只有root目录或者没有生成有效名称，使用默认名称
            if (zipFileName.length() == 0) {
                zipFileName.append("批量下载文档");
            }
            zipFileName.append(".zip");

            String encodedFileName = null;
            try {
                encodedFileName = java.net.URLEncoder.encode(zipFileName.toString(), "UTF-8").replaceAll("\\+", "%20");
            } catch (Exception e) {
                log.error("文件名编码失败", e);
                encodedFileName = "documents.zip";
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            // 使用正确的格式设置Content-Disposition
            // 先设置标准格式，针对RFC 6266
            headers.set("Content-Disposition", "attachment; filename=\"" + zipFileName + "\"; filename*=UTF-8''" + encodedFileName);

            log.info("设置下载文件名: {}", zipFileName.toString());

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(baos.toByteArray());
        } catch (Exception e) {
            log.error("批量下载Word文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 批量物理删除Word文件和数据库记录
     */
    @DeleteMapping("/batchPhysicalDeleteWords")
    public ResponseEntity<Map<String, Object>> batchPhysicalDeleteWords(@RequestBody List<Long> ids) {
        try {
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();
            
            for (Long id : ids) {
                try {
                    boolean success = taskService.physicalDeleteTask(id);
                    if (success) {
                        successCount++;
                    } else {
                        failCount++;
                        errorMessages.append("ID ").append(id).append(": 物理删除失败\n");
                    }
                } catch (Exception e) {
                    failCount++;
                    errorMessages.append("ID ").append(id).append(": ").append(e.getMessage()).append("\n");
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "批量物理删除完成: " + successCount + " 个成功, " + failCount + " 个失败");
            if (failCount > 0) {
                result.put("errorDetail", errorMessages.toString());
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量物理删除Word文件和数据库记录失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "批量物理删除失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
} 