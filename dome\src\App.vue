<template>
  <div id="app">
    <el-container>
      <el-header>
        <el-menu mode="horizontal" router :default-active="activeIndex" background-color="#409EFF" text-color="#fff" active-text-color="#ffd04b">
          <div class="logo">豆包API批量调用系统</div>
          <el-menu-item index="/">首页</el-menu-item>
          <el-menu-item index="/image-management">图片管理</el-menu-item>
          <el-menu-item index="/task-management">任务管理</el-menu-item>
          <el-menu-item index="/task-progress">任务进度</el-menu-item>
        </el-menu>
      </el-header>
      <el-main>
        <router-view/>
      </el-main>
      <el-footer>
        豆包API批量调用系统 &copy; 2025
      </el-footer>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      activeIndex: '/'
    }
  },
  watch: {
    $route: {
      immediate: true,
      handler(to) {
        this.activeIndex = to.path;
      }
    }
  }
}
</script>

<style>
body {
  margin: 0;
  padding: 0;
}
#app {
  font-family: 'Microsoft YaHei', 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100vh;
}
.el-container {
  height: 100%;
}
.el-header {
  padding: 0;
  height: auto !important;
}
.el-main {
  background-color: #f5f7fa;
  padding: 20px;
}
.el-footer {
  text-align: center;
  line-height: 60px;
  background-color: #f5f7fa;
  color: #909399;
}
.logo {
  float: left;
  height: 60px;
  line-height: 60px;
  margin: 0 20px;
  color: #fff;
  font-size: 20px;
  font-weight: bold;
}
</style> 