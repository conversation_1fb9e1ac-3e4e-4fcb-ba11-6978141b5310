<template>
  <div class="failed-tasks">
    <el-card>
      <div slot="header">
        <h2>失败任务</h2>
        <div class="header-action">
          <el-button type="primary" size="small" icon="el-icon-refresh" @click="fetchFailedTasks">刷新</el-button>
          <el-button 
            type="warning" 
            size="small" 
            icon="el-icon-refresh-right" 
            :disabled="selectedTasks.length === 0" 
            @click="retrySelectedTasks">重试选中任务</el-button>
        </div>
      </div>
      
      <!-- 失败任务列表 -->
      <div v-loading="loading">
        <el-empty v-if="failedTasks.length === 0" description="没有失败任务"></el-empty>
        
        <div v-else class="custom-table">
          <!-- 表头 -->
          <div class="table-header">
            <div class="table-cell selection">
              <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange"></el-checkbox>
            </div>
            <div class="table-cell id">ID</div>
            <div class="table-cell filename">文件名</div>
            <div class="table-cell photoname">图片名称</div>
            <div class="table-cell prompt">提示词</div>
            <div class="table-cell error">错误信息</div>
            <div class="table-cell batchname">批次名称</div>
            <div class="table-cell batchno">批次号</div>
            <div class="table-cell action">操作</div>
          </div>
          
          <!-- 表格内容 -->
          <div class="table-body">
            <div 
              v-for="task in failedTasks" 
              :key="task.id" 
              class="table-row"
              :class="{'selected': selectedTaskIds.includes(task.id)}"
            >
              <div class="table-cell selection">
                <el-checkbox 
                  v-model="task.checked" 
                  @change="handleCheckChange(task)"
                ></el-checkbox>
              </div>
              <div class="table-cell id">{{ task.id }}</div>
              <div class="table-cell filename">{{ task.fileName }}</div>
              <div class="table-cell photoname">{{ task.photoName || '-' }}</div>
              <div class="table-cell prompt" :title="task.prompt">{{ task.prompt }}</div>
              <div class="table-cell error" :title="task.errorMsg">{{ task.errorMsg }}</div>
              <div class="table-cell batchname">{{ task.batchName || '-' }}</div>
              <div class="table-cell batchno">{{ task.batchNo }}</div>
              <div class="table-cell action">
                <el-button 
                  size="mini" 
                  type="warning" 
                  @click="retryTask(task)">重试</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getFailedTasks, retryFailedTask, retryFailedTasks } from '@/api/task'

export default {
  name: 'FailedTasks',
  data() {
    return {
      loading: false,
      failedTasks: [],
      selectedTasks: [],
      selectedTaskIds: [],
      checkAll: false,
      isIndeterminate: false
    }
  },
  created() {
    this.fetchFailedTasks()
  },
  methods: {
    // 获取失败任务列表
    async fetchFailedTasks() {
      this.loading = true
      try {
        const res = await getFailedTasks()
        // 添加checked属性用于复选框
        this.failedTasks = (res.data || []).map(task => ({
          ...task,
          checked: false
        }))
        this.selectedTasks = []
        this.selectedTaskIds = []
        this.checkAll = false
        this.isIndeterminate = false
      } catch (error) {
        console.error('获取失败任务列表失败', error)
        this.$message.error('获取失败任务列表失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    
    // 重试单个任务
    async retryTask(task) {
      try {
        this.$message({
          message: '正在重试任务...',
          type: 'info'
        })
        
        const res = await retryFailedTask(task.id)
        if (res.code === 0) {
          this.$message.success('任务重试已启动')
          // 刷新列表
          this.fetchFailedTasks()
        } else {
          this.$message.error(res.message || '重试任务失败')
        }
      } catch (error) {
        console.error('重试任务失败', error)
        this.$message.error('重试任务失败：' + (error.message || '未知错误'))
      }
    },
    
    // 重试选中的任务
    async retrySelectedTasks() {
      if (this.selectedTasks.length === 0) {
        this.$message.warning('请先选择要重试的任务')
        return
      }
      
      try {
        const taskIds = this.selectedTasks.map(task => task.id)
        
        this.$message({
          message: `正在重试 ${taskIds.length} 个任务...`,
          type: 'info'
        })
        
        const res = await retryFailedTasks(taskIds)
        if (res.code === 0) {
          const data = res.data || {}
          this.$message.success(`任务重试已启动：${data.successCount || 0} 个成功, ${data.failCount || 0} 个失败`)
          // 刷新列表
          this.fetchFailedTasks()
        } else {
          this.$message.error(res.message || '批量重试任务失败')
        }
      } catch (error) {
        console.error('批量重试任务失败', error)
        this.$message.error('批量重试任务失败：' + (error.message || '未知错误'))
      }
    },
    
    // 处理全选/取消全选
    handleCheckAllChange(val) {
      this.failedTasks.forEach(task => {
        task.checked = val
      })
      this.updateSelectedTasks()
    },
    
    // 处理单个选择变化
    handleCheckChange(task) {
      this.updateSelectedTasks()
    },
    
    // 更新选中的任务列表
    updateSelectedTasks() {
      this.selectedTasks = this.failedTasks.filter(task => task.checked)
      this.selectedTaskIds = this.selectedTasks.map(task => task.id)
      
      const checkedCount = this.selectedTasks.length
      this.checkAll = checkedCount === this.failedTasks.length && this.failedTasks.length > 0
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.failedTasks.length
    }
  }
}
</script>

<style scoped>
.failed-tasks {
  margin: 0 auto;
}
.header-action {
  float: right;
  margin-top: -32px;
}

/* 自定义表格样式 */
.custom-table {
  width: 100%;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-header {
  display: grid;
  grid-template-columns: 50px 80px 200px 150px 1fr 1fr 150px 150px 120px;
  background-color: #F5F7FA;
  font-weight: bold;
  border-bottom: 1px solid #EBEEF5;
}

.table-row {
  display: grid;
  grid-template-columns: 50px 80px 200px 150px 1fr 1fr 150px 150px 120px;
  border-bottom: 1px solid #EBEEF5;
  transition: background-color 0.3s;
}

.table-row:hover {
  background-color: #F5F7FA;
}

.table-row.selected {
  background-color: #ecf5ff;
}

.table-cell {
  padding: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.table-cell.selection {
  justify-content: center;
}

.table-cell.prompt, .table-cell.error {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-cell.action {
  justify-content: center;
}
</style> 