package com.example.dobao.controller;

import com.example.dobao.entity.ImageInfo;
import com.example.dobao.entity.SealLineRelation;
import com.example.dobao.service.ImageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图片管理控制器
 */
@RestController
@RequestMapping("/api/images")
@Slf4j
public class ImageController {

    @javax.annotation.Resource
    private ImageService imageService;

    /**
     * 上传图片
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadImage(
            @RequestParam("imageName") String imageName,
            @RequestParam("file") MultipartFile file) {
        try {
            ImageInfo imageInfo = imageService.uploadImage(imageName, file);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "上传成功");
            result.put("data", imageInfo);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("上传图片失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "上传失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取所有图片
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getAllImages() {
        try {
            List<ImageInfo> images = imageService.getAllImages();
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "获取成功");
            result.put("data", images);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取图片列表失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "获取失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 删除图片
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteImage(@PathVariable Long id) {
        try {
            boolean success = imageService.deleteImage(id);
            
            Map<String, Object> result = new HashMap<>();
            if (success) {
                result.put("code", 0);
                result.put("message", "删除成功");
            } else {
                result.put("code", 1);
                result.put("message", "删除失败，图片不存在");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除图片失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "删除失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 根据ID获取图片
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getImageById(@PathVariable Long id) {
        try {
            ImageInfo imageInfo = imageService.getImageById(id);
            
            Map<String, Object> result = new HashMap<>();
            if (imageInfo != null) {
                result.put("code", 0);
                result.put("message", "获取成功");
                result.put("data", imageInfo);
                
                // 如果有密封线关联，一并返回
                SealLineRelation relation = imageService.getSealLineRelation(id);
                if (relation != null) {
                    result.put("sealLineRelation", relation);
                }
            } else {
                result.put("code", 1);
                result.put("message", "图片不存在");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取图片失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "获取失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取图片文件
     */
    @GetMapping("/file/{id}")
    public ResponseEntity<Resource> getImageFile(@PathVariable Long id) {
        try {
            ImageInfo imageInfo = imageService.getImageById(id);
            if (imageInfo != null) {
                File file = new File(imageInfo.getImagePath());
                if (file.exists()) {
                    String contentType = Files.probeContentType(Paths.get(file.getAbsolutePath()));
                    if (contentType == null) {
                        contentType = "application/octet-stream";
                    }
                    
                    return ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(contentType))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + imageInfo.getImageName() + "." + imageInfo.getImageType() + "\"")
                            .body(new FileSystemResource(file));
                }
            }
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("获取图片文件失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新图片信息（仅更新元数据）
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateImage(
            @PathVariable Long id,
            @RequestBody ImageInfo imageInfo) {
        try {
            imageInfo.setId(id); // 确保ID一致
            boolean success = imageService.updateImage(imageInfo);
            
            Map<String, Object> result = new HashMap<>();
            if (success) {
                result.put("code", 0);
                result.put("message", "更新成功");
                result.put("data", imageService.getImageById(id));
            } else {
                result.put("code", 1);
                result.put("message", "更新失败，图片不存在");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新图片信息失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "更新失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 更新图片（包括图片文件）
     */
    @PutMapping(value = "/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Map<String, Object>> updateImageWithFile(
            @PathVariable Long id,
            @RequestParam("imageName") String imageName,
            @RequestParam("file") MultipartFile file) {
        try {
            ImageInfo imageInfo = imageService.updateImageWithFile(id, imageName, file);
            
            Map<String, Object> result = new HashMap<>();
            if (imageInfo != null) {
                result.put("code", 0);
                result.put("message", "更新图片成功");
                result.put("data", imageInfo);
            } else {
                result.put("code", 1);
                result.put("message", "更新失败，图片不存在");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新图片失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "更新失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 上传密封线图片
     */
    @PostMapping("/seal-line/upload")
    public ResponseEntity<Map<String, Object>> uploadSealLineImage(
            @RequestParam("imageName") String imageName,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "sealLineConfig", required = false) String sealLineConfig) {
        try {
            ImageInfo imageInfo = imageService.uploadSealLineImage(imageName, file, sealLineConfig);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "上传密封线图片成功");
            result.put("data", imageInfo);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("上传密封线图片失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "上传失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 关联密封线图片
     */
    @PostMapping("/{imageId}/seal-line/{sealLineId}")
    public ResponseEntity<Map<String, Object>> associateSealLine(
            @PathVariable Long imageId,
            @PathVariable Long sealLineId,
            @RequestParam(value = "config", required = false) String config) {
        try {
            SealLineRelation relation = imageService.associateSealLine(imageId, sealLineId, config);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "关联密封线图片成功");
            result.put("data", relation);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("关联密封线图片失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "关联失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取图片的密封线关联
     */
    @GetMapping("/{imageId}/seal-line")
    public ResponseEntity<Map<String, Object>> getSealLineRelation(@PathVariable Long imageId) {
        try {
            SealLineRelation relation = imageService.getSealLineRelation(imageId);
            
            Map<String, Object> result = new HashMap<>();
            if (relation != null) {
                ImageInfo sealLineImage = imageService.getImageById(relation.getSealLineId());
                
                result.put("code", 0);
                result.put("message", "获取成功");
                result.put("data", relation);
                result.put("sealLineImage", sealLineImage);
            } else {
                result.put("code", 1);
                result.put("message", "该图片未关联密封线图片");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取密封线关联失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "获取失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 删除图片的密封线关联
     */
    @DeleteMapping("/{imageId}/seal-line")
    public ResponseEntity<Map<String, Object>> removeSealLineRelation(@PathVariable Long imageId) {
        try {
            boolean success = imageService.removeSealLineRelation(imageId);
            
            Map<String, Object> result = new HashMap<>();
            if (success) {
                result.put("code", 0);
                result.put("message", "删除密封线关联成功");
            } else {
                result.put("code", 1);
                result.put("message", "删除失败，可能该图片未关联密封线图片");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除密封线关联失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("message", "删除失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
} 