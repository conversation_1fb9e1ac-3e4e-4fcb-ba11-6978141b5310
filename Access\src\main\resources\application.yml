server:
  port: 8088
  servlet:
    context-path: /dobao

spring:
  application:
    name: dobao
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************
    username: root
    password: 123456
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# MyBatis 配置
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 豆包API配置
doubao:
  api:
    key: 0f687ffa-307d-4957-b47d-ed2a9067f487
  model:
    vision: doubao-1.5-pro-32k-250115
    thinking: doubao-1.5-pro-32k-250115
  thread:
    pool:
      size: 8

# 上传路径配置
upload:
  image:
    path: D:/admin/5-6/Access/upload/images
  word:
    path: D:/admin/5-6/Access/upload/words 