<template>
  <div class="image-management">
    <el-card>
      <div slot="header">
        <h2>图片管理</h2>
        <div class="header-action">
          <el-button type="primary" size="small" @click="handleUploadClick('normal')">上传图片</el-button>
          <el-button type="success" size="small" @click="handleUploadClick('sealLine')">上传密封线图片</el-button>
        </div>
      </div>
      
      <!-- 上传图片对话框 -->
      <el-dialog :title="uploadType === 'sealLine' ? '上传密封线图片' : '上传图片'" :visible.sync="uploadDialogVisible" width="500px">
        <el-form :model="uploadForm" :rules="uploadRules" ref="uploadForm" label-width="100px">
          <el-form-item label="图片名称" prop="imageName">
            <el-input v-model="uploadForm.imageName" placeholder="请输入图片名称，用于在任务中引用"></el-input>
          </el-form-item>
          <el-form-item label="图片文件" prop="file">
            <el-upload
              class="image-uploader"
              action="#"
              :http-request="handleUpload"
              :show-file-list="false"
              :before-upload="beforeUpload"
              accept=".jpg,.jpeg,.png">
              <img v-if="imageUrl" :src="imageUrl" class="uploaded-image">
              <i v-else class="el-icon-plus image-uploader-icon"></i>
              <div class="el-upload__tip" slot="tip">只能上传JPG/PNG文件，且不超过10MB</div>
            </el-upload>
          </el-form-item>
          <el-form-item v-if="uploadType === 'sealLine'" label="配置信息" prop="sealLineConfig">
            <el-input
              type="textarea"
              :rows="4"
              placeholder="请输入密封线配置信息（JSON格式）"
              v-model="uploadForm.sealLineConfig">
            </el-input>
            <div class="el-upload__tip">密封线配置信息，用于在Word中插入密封线图片时使用</div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploading">确 定</el-button>
        </div>
      </el-dialog>
      
      <!-- 编辑图片对话框 -->
      <el-dialog title="编辑图片" :visible.sync="editDialogVisible" width="500px">
        <el-form :model="editForm" :rules="editRules" ref="editForm" label-width="100px">
          <el-form-item label="图片名称" prop="imageName">
            <el-input v-model="editForm.imageName" placeholder="请输入图片名称"></el-input>
          </el-form-item>
          <el-form-item label="图片预览">
            <img :src="getImageUrl(editForm)" class="edit-image-preview" alt="图片预览">
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitEdit" :loading="editing">确 定</el-button>
        </div>
      </el-dialog>
      
      <!-- 关联密封线图片对话框 -->
      <el-dialog title="关联密封线图片" :visible.sync="associateDialogVisible" width="650px">
        <div v-if="associateDialogVisible">
          <el-form :model="associateForm" :rules="associateRules" ref="associateForm" label-width="100px">
            <el-form-item label="原图片">
              <div class="associate-preview">
                <img :src="getImageUrl(currentImage)" class="preview-image" alt="原图片">
                <div class="image-info">{{ currentImage.imageName }}</div>
              </div>
            </el-form-item>
            <el-form-item label="密封线图片" prop="sealLineId">
              <el-select v-model="associateForm.sealLineId" placeholder="请选择密封线图片" style="width: 100%">
                <el-option
                  v-for="image in sealLineImages"
                  :key="image.id"
                  :label="image.imageName"
                  :value="image.id">
                  <div class="select-option">
                    <img :src="getImageUrl(image)" class="option-image">
                    <span>{{ image.imageName }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="配置信息" prop="config">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入密封线配置信息（JSON格式）"
                v-model="associateForm.config">
              </el-input>
            </el-form-item>
          </el-form>
          <div class="preview-container" v-if="associateForm.sealLineId">
            <h4>密封线图片预览</h4>
            <img :src="getSealLineImageUrl()" class="seal-line-preview-image">
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="associateDialogVisible = false">取 消</el-button>
          <el-button 
            type="danger" 
            v-if="hasSealLineRelation"
            @click="handleRemoveSealLine" 
            :loading="removing">
            移除关联
          </el-button>
          <el-button type="primary" @click="submitAssociate" :loading="associating">确 定</el-button>
        </div>
      </el-dialog>
      
      <!-- 图片列表 -->
      <div v-loading="loading">
        <el-empty v-if="images.length === 0" description="暂无图片，请上传"></el-empty>
        <el-row :gutter="20" v-else>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="image in images" :key="image.id" class="image-item">
            <el-card :body-style="{ padding: '0px' }">
              <div class="image-preview">
                <img 
                  :src="getImageUrl(image)" 
                  class="image" 
                  @error="(e) => handleImageError(image, e)"
                  :alt="image.imageName">
                <el-tag v-if="image.isSealLine" size="mini" type="success" class="seal-line-tag">密封线</el-tag>
                <el-tag v-if="getSealLineTag(image)" size="mini" type="warning" class="has-seal-line-tag">已关联密封线</el-tag>
              </div>
              <div style="padding: 14px;">
                <div class="image-name">{{ image.imageName }}</div>
                <div class="image-info">
                  <span>{{ formatFileSize(image.imageSize) }}</span>
                  <span>{{ image.imageType.toUpperCase() }}</span>
                </div>
                <div class="image-actions">
                  <el-button type="text" @click="handleCopyName(image.imageName)">复制名称</el-button>
                  <el-button type="text" @click="handleEdit(image)">编辑</el-button>
                  <el-button type="text" @click="handleAssociate(image)" v-if="!image.isSealLine">关联密封线</el-button>
                  <el-button type="text" class="button-delete" @click="handleDelete(image)">删除</el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import { uploadImage, getAllImages, deleteImage, updateImage, uploadSealLineImage, 
  associateSealLine, getSealLineRelation, removeSealLineRelation } from '@/api/image'
import request from '@/api/config'

export default {
  name: 'ImageManagement',
  data() {
    return {
      loading: false,
      uploading: false,
      editing: false,
      associating: false,
      removing: false,
      images: [],
      sealLineImages: [],
      uploadDialogVisible: false,
      editDialogVisible: false,
      associateDialogVisible: false,
      imageUrl: '',
      uploadType: 'normal', // 'normal'或'sealLine'
      uploadForm: {
        imageName: '',
        file: null,
        sealLineConfig: ''
      },
      editForm: {
        id: null,
        imageName: ''
      },
      currentImage: null,
      associateForm: {
        sealLineId: null,
        config: ''
      },
      hasSealLineRelation: false,
      imageToSealLineMap: new Map(), // 图片ID到密封线关联的映射
      uploadRules: {
        imageName: [
          { required: true, message: '请输入图片名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        file: [
          { required: true, message: '请选择图片文件', trigger: 'change' }
        ],
        sealLineConfig: [
          { validator: this.validateJSON, trigger: 'blur' }
        ]
      },
      editRules: {
        imageName: [
          { required: true, message: '请输入图片名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ]
      },
      associateRules: {
        sealLineId: [
          { required: true, message: '请选择密封线图片', trigger: 'change' }
        ],
        config: [
          { validator: this.validateJSON, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.fetchImages()
  },
  methods: {
    // 校验JSON格式
    validateJSON(rule, value, callback) {
      if (!value) {
        callback()
        return
      }
      try {
        JSON.parse(value)
        callback()
      } catch (error) {
        callback(new Error('请输入正确的JSON格式'))
      }
    },
    
    // 获取所有图片
    async fetchImages() {
      this.loading = true
      try {
        const res = await getAllImages()
        this.images = res.data || []
        
        // 过滤出密封线图片
        this.sealLineImages = this.images.filter(image => image.isSealLine)
        
        // 清空密封线关联映射
        this.imageToSealLineMap.clear()
        
        // 获取每个非密封线图片的密封线关联
        const normalImages = this.images.filter(image => !image.isSealLine)
        for (const image of normalImages) {
          this.fetchSealLineRelation(image.id)
        }
      } catch (error) {
        console.error('获取图片列表失败', error)
      } finally {
        this.loading = false
      }
    },
    
    // 获取图片的密封线关联
    async fetchSealLineRelation(imageId) {
      try {
        const res = await getSealLineRelation(imageId)
        if (res.code === 0 && res.data) {
          this.imageToSealLineMap.set(imageId, {
            relationId: res.data.id,
            sealLineId: res.data.sealLineId,
            config: res.data.config
          })
        }
      } catch (error) {
        console.error(`获取图片[${imageId}]的密封线关联失败`, error)
      }
    },
    
    // 判断图片是否有密封线关联
    getSealLineTag(image) {
      return this.imageToSealLineMap.has(image.id)
    },
    
    // 打开上传对话框
    handleUploadClick(type) {
      this.uploadType = type
      this.uploadDialogVisible = true
      this.uploadForm = {
        imageName: '',
        file: null,
        sealLineConfig: type === 'sealLine' ? '{"position": "bottom", "height": 30}' : ''
      }
      this.imageUrl = ''
      // 下一次DOM更新后重置表单校验结果
      this.$nextTick(() => {
        if (this.$refs.uploadForm) {
          this.$refs.uploadForm.clearValidate()
        }
      })
    },
    
    // 打开编辑对话框
    handleEdit(image) {
      this.editDialogVisible = true
      this.editForm = {
        id: image.id,
        imageName: image.imageName
      }
      // 下一次DOM更新后重置表单校验结果
      this.$nextTick(() => {
        if (this.$refs.editForm) {
          this.$refs.editForm.clearValidate()
        }
      })
    },
    
    // 打开关联密封线图片对话框
    handleAssociate(image) {
      this.currentImage = image
      this.associateDialogVisible = true
      
      // 检查是否已有关联
      const relation = this.imageToSealLineMap.get(image.id)
      if (relation) {
        this.hasSealLineRelation = true
        this.associateForm = {
          sealLineId: relation.sealLineId,
          config: relation.config || ''
        }
      } else {
        this.hasSealLineRelation = false
        this.associateForm = {
          sealLineId: null,
          config: '{"position": "bottom", "height": 30}'
        }
      }
      
      // 下一次DOM更新后重置表单校验结果
      this.$nextTick(() => {
        if (this.$refs.associateForm) {
          this.$refs.associateForm.clearValidate()
        }
      })
    },
    
    // 获取选中的密封线图片URL
    getSealLineImageUrl() {
      if (!this.associateForm.sealLineId) return ''
      const image = this.sealLineImages.find(img => img.id === this.associateForm.sealLineId)
      return image ? this.getImageUrl(image) : ''
    },
    
    // 上传前校验
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isJPG && !isPNG) {
        this.$message.error('图片只能是 JPG 或 PNG 格式!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('图片大小不能超过 10MB!')
        return false
      }
      
      // 预览图片
      this.imageUrl = URL.createObjectURL(file)
      this.uploadForm.file = file
      return false // 阻止自动上传
    },
    
    // 自定义上传
    handleUpload(options) {
      // 这个方法不会被调用，因为beforeUpload返回false阻止了自动上传
      // 但el-upload需要这个方法
    },
    
    // 提交上传
    submitUpload() {
      this.$refs.uploadForm.validate(async (valid) => {
        if (valid) {
          this.uploading = true
          try {
            const formData = new FormData()
            formData.append('imageName', this.uploadForm.imageName)
            formData.append('file', this.uploadForm.file)
            
            if (this.uploadType === 'sealLine' && this.uploadForm.sealLineConfig) {
              formData.append('sealLineConfig', this.uploadForm.sealLineConfig)
              await uploadSealLineImage(formData)
            } else {
              await uploadImage(formData)
            }
            
            this.$message.success('上传成功')
            this.uploadDialogVisible = false
            this.fetchImages()
          } catch (error) {
            console.error('上传失败', error)
            // 显示更详细的错误信息
            const errorMsg = error.response && error.response.data && error.response.data.message
              ? error.response.data.message
              : '上传失败，可能是服务端存储路径不存在，请联系管理员检查upload/images目录是否已创建'
            this.$message.error(errorMsg)
          } finally {
            this.uploading = false
          }
        } else {
          return false
        }
      })
    },
    
    // 提交编辑
    submitEdit() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          this.editing = true
          try {
            await updateImage(this.editForm.id, {
              imageName: this.editForm.imageName
            })
            this.$message.success('更新成功')
            this.editDialogVisible = false
            this.fetchImages()
          } catch (error) {
            console.error('更新失败', error)
            this.$message.error('更新失败: ' + (error.response?.data?.message || error.message || '未知错误'))
          } finally {
            this.editing = false
          }
        } else {
          return false
        }
      })
    },
    
    // 提交密封线关联
    submitAssociate() {
      this.$refs.associateForm.validate(async (valid) => {
        if (valid) {
          this.associating = true
          try {
            await associateSealLine(
              this.currentImage.id,
              this.associateForm.sealLineId,
              this.associateForm.config
            )
            this.$message.success('关联密封线图片成功')
            this.associateDialogVisible = false
            // 更新关联状态
            this.fetchSealLineRelation(this.currentImage.id)
          } catch (error) {
            console.error('关联密封线图片失败', error)
            this.$message.error('关联失败: ' + (error.response?.data?.message || error.message || '未知错误'))
          } finally {
            this.associating = false
          }
        } else {
          return false
        }
      })
    },
    
    // 移除密封线关联
    async handleRemoveSealLine() {
      this.removing = true
      try {
        await removeSealLineRelation(this.currentImage.id)
        this.$message.success('移除密封线关联成功')
        this.associateDialogVisible = false
        this.imageToSealLineMap.delete(this.currentImage.id)
      } catch (error) {
        console.error('移除密封线关联失败', error)
        this.$message.error('移除失败: ' + (error.response?.data?.message || error.message || '未知错误'))
      } finally {
        this.removing = false
      }
    },
    
    // 删除图片
    handleDelete(image) {
      this.$confirm(`确定要删除图片 "${image.imageName}" 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteImage(image.id)
          this.$message.success('删除成功')
          this.fetchImages()
        } catch (error) {
          console.error('删除失败', error)
        }
      }).catch(() => {
        // 取消删除，不做任何操作
      })
    },
    
    // 复制图片名称
    handleCopyName(name) {
      const input = document.createElement('input')
      input.value = name
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      document.body.removeChild(input)
      this.$message.success('复制成功')
    },
    
    // 格式化文件大小
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB'
      } else {
        return (size / 1024 / 1024).toFixed(2) + ' MB'
      }
    },
    
    // 获取图片URL
    getImageUrl(image) {
      if (!image || !image.id) return ''
      // 从axios配置中提取baseURL，避免硬编码
      const baseURL = request.defaults.baseURL;
      return `${baseURL}/api/images/file/${image.id}`
    },
    
    // 处理图片加载失败
    handleImageError(image, event) {
      console.error(`图片加载失败: ${image.imageName}`, image)
      // 使用事件目标（即img元素）设置备用图片
      const imgElement = event.target
      imgElement.src = `https://via.placeholder.com/200x200?text=${image.imageName}`
      imgElement.classList.add('image-error')
      
      // 显示错误提示
      this.$notify({
        title: '图片加载失败',
        message: `无法加载图片"${image.imageName}"，显示占位图`,
        type: 'warning',
        duration: 3000
      })
    }
  }
}
</script>

<style scoped>
.image-management {
  margin: 0 auto;
}
.header-action {
  float: right;
  margin-top: -32px;
}
.image-uploader {
  text-align: center;
}
.image-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.image-uploader .el-upload:hover {
  border-color: #409EFF;
}
.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.uploaded-image {
  width: 178px;
  height: 178px;
  display: block;
  object-fit: cover;
}
.image-item {
  margin-bottom: 20px;
}
.image-preview {
  width: 100%;
  height: 150px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  position: relative;
}
.image-preview .image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
.image-name {
  font-size: 14px;
  color: #303133;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-bottom: 8px;
}
.image-info {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
}
.image-actions {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.button-delete {
  color: #F56C6C;
}
.image-error {
  border: 1px dashed #F56C6C;
  opacity: 0.8;
}
.edit-image-preview {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  display: block;
  margin: 0 auto;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}
.seal-line-tag {
  position: absolute;
  top: 5px;
  right: 5px;
}
.has-seal-line-tag {
  position: absolute;
  top: 5px;
  left: 5px;
}
.associate-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.preview-image {
  max-width: 100%;
  max-height: 150px;
  object-fit: contain;
  margin-bottom: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}
.select-option {
  display: flex;
  align-items: center;
}
.option-image {
  width: 30px;
  height: 30px;
  object-fit: cover;
  margin-right: 10px;
  border-radius: 2px;
}
.preview-container {
  margin-top: 20px;
  text-align: center;
}
.seal-line-preview-image {
  max-width: 100%;
  max-height: 150px;
  object-fit: contain;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}
</style> 