package com.example.dobao.service.impl;

import com.example.dobao.entity.ImageInfo;
import com.example.dobao.entity.SealLineRelation;
import com.example.dobao.mapper.ImageInfoMapper;
import com.example.dobao.mapper.SealLineRelationMapper;
import com.example.dobao.service.ImageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;
import java.util.UUID;
import java.util.Date;

/**
 * 图片服务实现类
 */
@Service
@Slf4j
public class ImageServiceImpl implements ImageService {

    @javax.annotation.Resource
    private ImageInfoMapper imageInfoMapper;
    
    @javax.annotation.Resource
    private SealLineRelationMapper sealLineRelationMapper;

    @Value("${upload.image.path:./upload/images}")
    private String uploadPath;

    @Override
    public ImageInfo uploadImage(String imageName, MultipartFile file) {
        try {
            // 检查文件类型
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!("png".equals(fileExtension) || "jpg".equals(fileExtension) || "jpeg".equals(fileExtension))) {
                throw new IllegalArgumentException("只支持PNG和JPG格式的图片");
            }

            // 使用绝对路径
            String absolutePath = new File(uploadPath).getAbsolutePath();
            log.info("上传目录绝对路径: {}", absolutePath);
            
            // 检查上传目录是否存在，不存在则创建
            File uploadDir = new File(absolutePath);
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                log.info("创建目录结果: {}, 路径: {}", created, uploadDir.getAbsolutePath());
                if (!created) {
                    // 尝试创建父目录
                    File parentDir = uploadDir.getParentFile();
                    if (parentDir != null && !parentDir.exists()) {
                        boolean parentCreated = parentDir.mkdirs();
                        log.info("创建父目录结果: {}, 路径: {}", parentCreated, parentDir.getAbsolutePath());
                    }
                    // 再次尝试创建目录
                    created = uploadDir.mkdirs();
                    log.info("第二次创建目录结果: {}", created);
                }
            }

            // 生成唯一文件名
            String fileName = UUID.randomUUID().toString() + "." + fileExtension;
            File destFile = new File(uploadDir, fileName);
            log.info("目标文件路径: {}", destFile.getAbsolutePath());

            // 保存文件
            file.transferTo(destFile);
            log.info("文件保存成功: {}", destFile.getAbsolutePath());

            // 保存数据库记录
            ImageInfo imageInfo = new ImageInfo();
            imageInfo.setImageName(imageName);
            imageInfo.setImagePath(destFile.getPath());
            imageInfo.setImageType(fileExtension);
            imageInfo.setImageSize(file.getSize());
            imageInfo.setIsSealLine(false);

            imageInfoMapper.save(imageInfo);
            log.info("图片信息保存到数据库成功，ID: {}", imageInfo.getId());
            
            return imageInfo;
        } catch (IOException e) {
            log.error("上传图片失败: {}", e.getMessage(), e);
            throw new RuntimeException("上传图片失败: " + e.getMessage());
        }
    }

    @Override
    public ImageInfo getImageById(Long id) {
        return imageInfoMapper.findById(id);
    }

    @Override
    public ImageInfo getImageByName(String imageName) {
        return imageInfoMapper.findByImageName(imageName);
    }

    @Override
    public List<ImageInfo> getAllImages() {
        return imageInfoMapper.findAll();
    }

    @Override
    @Transactional
    public boolean deleteImage(Long id) {
        ImageInfo imageInfo = imageInfoMapper.findById(id);
        if (imageInfo != null) {
            // 删除关联关系
            sealLineRelationMapper.deleteByImageId(id);
            sealLineRelationMapper.deleteBySealLineId(id);
            
            // 删除文件
            File file = new File(imageInfo.getImagePath());
            if (file.exists()) {
                file.delete();
            }
            
            // 删除数据库记录
            return imageInfoMapper.deleteById(id) > 0;
        }
        return false;
    }

    @Override
    public String getImageBase64(String imageName) {
        ImageInfo imageInfo = imageInfoMapper.findByImageName(imageName);
        if (imageInfo != null) {
            try {
                File file = new File(imageInfo.getImagePath());
                if (file.exists()) {
                    byte[] fileContent = Files.readAllBytes(file.toPath());
                    return Base64.getEncoder().encodeToString(fileContent);
                }
            } catch (IOException e) {
                log.error("读取图片失败", e);
            }
        }
        return null;
    }
    
    @Override
    public boolean updateImage(ImageInfo imageInfo) {
        try {
            // 查询原图片信息
            ImageInfo oldImageInfo = imageInfoMapper.findById(imageInfo.getId());
            if (oldImageInfo == null) {
                return false;
            }
            
            // 设置不允许修改的字段
            imageInfo.setImagePath(oldImageInfo.getImagePath());
            imageInfo.setImageType(oldImageInfo.getImageType());
            imageInfo.setImageSize(oldImageInfo.getImageSize());
            imageInfo.setCreateTime(oldImageInfo.getCreateTime());
            imageInfo.setUpdateTime(new Date());
            
            // 更新数据库
            return imageInfoMapper.update(imageInfo) > 0;
        } catch (Exception e) {
            log.error("更新图片信息失败", e);
            return false;
        }
    }
    
    @Override
    public ImageInfo uploadSealLineImage(String imageName, MultipartFile file, String sealLineConfig) {
        try {
            // 使用普通上传方法保存图片
            ImageInfo imageInfo = uploadImage(imageName, file);
            
            // 设置密封线相关字段
            imageInfo.setIsSealLine(true);
            imageInfo.setSealLineConfig(sealLineConfig);
            
            // 更新数据库
            imageInfoMapper.update(imageInfo);
            
            return imageInfo;
        } catch (Exception e) {
            log.error("上传密封线图片失败: {}", e.getMessage(), e);
            throw new RuntimeException("上传密封线图片失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public SealLineRelation associateSealLine(Long imageId, Long sealLineId, String config) {
        try {
            // 验证图片是否存在
            ImageInfo imageInfo = imageInfoMapper.findById(imageId);
            if (imageInfo == null) {
                throw new RuntimeException("原图片不存在");
            }
            
            ImageInfo sealLineImage = imageInfoMapper.findById(sealLineId);
            if (sealLineImage == null) {
                throw new RuntimeException("密封线图片不存在");
            }
            
            // 检查是否已有关联，如果有则更新
            SealLineRelation existingRelation = sealLineRelationMapper.findByImageId(imageId);
            if (existingRelation != null) {
                existingRelation.setSealLineId(sealLineId);
                existingRelation.setConfig(config);
                sealLineRelationMapper.update(existingRelation);
                return existingRelation;
            }
            
            // 创建新的关联
            SealLineRelation relation = new SealLineRelation();
            relation.setImageId(imageId);
            relation.setSealLineId(sealLineId);
            relation.setConfig(config);
            
            sealLineRelationMapper.save(relation);
            
            return relation;
        } catch (Exception e) {
            log.error("关联密封线图片失败: {}", e.getMessage(), e);
            throw new RuntimeException("关联密封线图片失败: " + e.getMessage());
        }
    }
    
    @Override
    public SealLineRelation getSealLineRelation(Long imageId) {
        return sealLineRelationMapper.findByImageId(imageId);
    }
    
    @Override
    public boolean removeSealLineRelation(Long imageId) {
        try {
            return sealLineRelationMapper.deleteByImageId(imageId) > 0;
        } catch (Exception e) {
            log.error("删除密封线关联失败: {}", e.getMessage(), e);
            return false;
        }
    }
} 