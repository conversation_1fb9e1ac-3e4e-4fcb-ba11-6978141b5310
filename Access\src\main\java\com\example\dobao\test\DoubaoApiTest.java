package com.example.dobao.test;

import com.example.dobao.service.DoubaoApiService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 豆包API测试类
 * 用于测试豆包API调用是否正常
 */
@Component
public class DoubaoApiTest implements CommandLineRunner {

    @Resource
    private DoubaoApiService doubaoApiService;

    @Override
    public void run(String... args) {
        // 取消注释下面的代码以在应用启动时测试API调用
        testDoubaoApi();
    }

    public void testDoubaoApi() {
        try {
            System.out.println("=== 开始测试豆包API调用 ===");
            String result = doubaoApiService.callDoubaoApi("你好，请介绍一下自己", null);
            System.out.println("API调用结果: " + result);
            System.out.println("=== 豆包API调用测试完成 ===");
        } catch (Exception e) {
            System.err.println("测试豆包API调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}