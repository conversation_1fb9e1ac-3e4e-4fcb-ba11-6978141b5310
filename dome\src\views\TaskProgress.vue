<template>
  <div class="task-progress">
    <el-card>
      <div slot="header">
        <h2>任务进度</h2>
        <div class="header-action">
          <el-button type="primary" size="small" icon="el-icon-refresh" @click="fetchBatches">刷新</el-button>
          <el-button type="warning" size="small" icon="el-icon-warning-outline" @click="viewFailedTasks">查看失败任务</el-button>
          <el-button type="danger" size="small" icon="el-icon-time" @click="viewProcessingTasks">查看处理中任务</el-button>
        </div>
      </div>
      
      <!-- 批次列表 -->
      <div v-loading="loading">
        <el-empty v-if="batches.length === 0" description="暂无任务，请先导入Excel文件"></el-empty>
        <div v-else>
          <div v-for="batch in batches" :key="batch.batchNo" class="batch-card">
            <el-card shadow="hover">
              <div slot="header" class="batch-header">
                <h3>批次: {{ batch.batchName || batch.batchNo }}</h3>
                <div class="batch-actions">
                  <el-button 
                    type="primary" 
                    size="mini" 
                    @click="handleProcess(batch)"
                    :disabled="!canProcess(batch)">
                    开始处理
                  </el-button>
                  <el-button 
                    type="info" 
                    size="mini" 
                    @click="viewDetails(batch.batchNo)">
                    查看详情
                  </el-button>
                </div>
              </div>
              
              <div class="batch-info">
                <div class="progress-wrapper">
                  <div class="progress-header">
                    <span>处理进度</span>
                    <span>{{ batch.progress || 0 }}%</span>
                  </div>
                  <el-progress 
                    :percentage="batch.progress || 0" 
                    :status="getProgressStatus(batch)">
                  </el-progress>
                </div>
                
                <div class="status-counts">
                  <el-row :gutter="10">
                    <el-col :span="6">
                      <div class="status-item">
                        <div class="status-label">总数</div>
                        <div class="status-value">{{ batch.total || 0 }}</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="status-item pending">
                        <div class="status-label">待处理</div>
                        <div class="status-value">{{ batch.pending || 0 }}</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="status-item processing">
                        <div class="status-label">处理中</div>
                        <div class="status-value">{{ batch.processing || 0 }}</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="status-item completed">
                        <div class="status-label">已完成</div>
                        <div class="status-value">{{ batch.completed || 0 }}</div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
                
                <div class="status-tag">
                  <el-tag :type="getStatusType(batch)" size="medium">
                    {{ getStatusText(batch) }}
                  </el-tag>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 处理中的任务对话框 -->
    <el-dialog
      title="处理中的任务"
      :visible.sync="processingDialogVisible"
      width="80%">
      <div v-loading="processingTasksLoading">
        <el-empty v-if="processingTasks.length === 0" description="没有处理中的任务"></el-empty>
        <div v-else>
          <el-alert
            title="提示：这些任务可能因为网络问题或其他原因停止了处理，可以通过重启按钮恢复处理"
            type="warning"
            :closable="false"
            show-icon>
          </el-alert>
          <div class="dialog-toolbar">
            <el-button type="primary" size="small" @click="handleRestartSelected" :disabled="selectedProcessingIds.length === 0">重启选中任务</el-button>
            <el-button type="danger" size="small" @click="handleRestartAll">重启所有任务</el-button>
          </div>
          
          <!-- 使用div代替表格 -->
          <div class="task-table">
            <!-- 表头 -->
            <div class="task-header">
              <div class="task-cell cell-checkbox">
                <el-checkbox 
                  :value="selectedAll" 
                  @change="handleSelectAll">
                </el-checkbox>
              </div>
              <div class="task-cell cell-id">ID</div>
              <div class="task-cell cell-filename">文件名</div>
              <div class="task-cell cell-prompt">提示词</div>
              <div class="task-cell cell-photoname">图片名称</div>
              <div class="task-cell cell-batchname">批次名称</div>
              <div class="task-cell cell-createtime">创建时间</div>
              <div class="task-cell cell-operation">操作</div>
            </div>
            
            <!-- 表格内容 -->
            <div class="task-body">
              <div v-if="processingTasks.length === 0" class="empty-tip">
                没有处理中的任务
              </div>
              <div 
                v-for="task in processingTasks" 
                :key="task.id" 
                class="task-row"
                :class="{'row-selected': isSelected(task.id)}"
                @click="toggleSelectTask(task)">
                <div class="task-cell cell-checkbox" @click.stop>
                  <el-checkbox 
                    :value="isSelected(task.id)" 
                    @change="(val) => handleSelectTask(task, val)">
                  </el-checkbox>
                </div>
                <div class="task-cell cell-id">{{ task.id }}</div>
                <div class="task-cell cell-filename" :title="task.fileName">{{ task.fileName }}</div>
                <div class="task-cell cell-prompt" :title="task.prompt">{{ task.prompt }}</div>
                <div class="task-cell cell-photoname">{{ task.photoName || '-' }}</div>
                <div class="task-cell cell-batchname">{{ task.batchName || '-' }}</div>
                <div class="task-cell cell-createtime">{{ task.createTime }}</div>
                <div class="task-cell cell-operation">
                  <el-button
                    @click.stop="handleRestartTask(task)"
                    type="text"
                    size="small">
                    重启
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAllBatches, getTaskProgress, processTask, getProcessingTasks, restartProcessingTasks } from '@/api/task'

export default {
  name: 'TaskProgress',
  data() {
    return {
      loading: false,
      batches: [],
      // 轮询任务进度的定时器
      progressTimer: null,
      // 处理中的任务数据
      processingDialogVisible: false,
      processingTasksLoading: false,
      processingTasks: [],
      selectedProcessingTasks: [],
      selectedProcessingIds: [],
      selectedAll: false
    }
  },
  created() {
    this.fetchBatches()
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }
  },
  methods: {
    // 获取所有批次
    async fetchBatches() {
      this.loading = true
      try {
        const res = await getAllBatches()
        this.batches = []
        
        if (res.data && res.data.length > 0) {
          // 获取每个批次的详细信息
          const promises = res.data.map(batchNo => this.fetchBatchProgress(batchNo))
          await Promise.all(promises)
          
          // 按进度从低到高排序
          this.batches.sort((a, b) => {
            // 首先按状态排序：待处理 > 处理中 > 已完成
            const statusOrder = { '待处理': 0, '处理中': 1, '已完成': 2 }
            const statusA = this.getStatusText(a)
            const statusB = this.getStatusText(b)
            
            if (statusOrder[statusA] !== statusOrder[statusB]) {
              return statusOrder[statusA] - statusOrder[statusB]
            }
            
            // 其次按进度排序
            return a.progress - b.progress
          })
        }
      } catch (error) {
        console.error('获取批次列表失败', error)
      } finally {
        this.loading = false
      }
      
      // 开始定时轮询进度
      this.startProgressPolling()
    },
    
    // 获取批次进度
    async fetchBatchProgress(batchNo) {
      try {
        const res = await getTaskProgress(batchNo)
        if (res.data) {
          // 查找是否已经存在该批次
          const index = this.batches.findIndex(b => b.batchNo === batchNo)
          if (index > -1) {
            // 更新已存在的批次
            this.$set(this.batches, index, res.data)
          } else {
            // 添加新批次
            this.batches.push(res.data)
          }
        }
      } catch (error) {
        console.error(`获取批次 ${batchNo} 进度失败`, error)
      }
    },
    
    // 开始轮询进度
    startProgressPolling() {
      // 先清除可能存在的定时器
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
      }
      
      // 设置新的定时器，每5秒轮询一次
      this.progressTimer = setInterval(() => {
        if (this.batches.length > 0) {
          // 只轮询状态不是完成或失败的批次
          const incompleteBatches = this.batches.filter(b => 
            b.progress < 100 && !(b.completed + b.failed === b.total && b.total > 0)
          )
          
          if (incompleteBatches.length > 0) {
            incompleteBatches.forEach(batch => {
              this.fetchBatchProgress(batch.batchNo)
            })
          } else {
            // 如果所有批次都完成了，停止轮询
            clearInterval(this.progressTimer)
            this.progressTimer = null
          }
        }
      }, 5000)
    },
    
    // 开始处理任务
    async handleProcess(batch) {
      try {
        await processTask(batch.batchNo)
        this.$message.success('任务处理已启动')
        
        // 更新该批次的状态
        await this.fetchBatchProgress(batch.batchNo)
        
        // 确保轮询正在进行
        this.startProgressPolling()
      } catch (error) {
        console.error('启动任务处理失败', error)
      }
    },
    
    // 查看任务详情
    viewDetails(batchNo) {
      this.$router.push(`/task-detail/${batchNo}`)
    },
    
    // 获取进度状态
    getProgressStatus(batch) {
      if (batch.failed > 0) {
        return 'exception';
      }
      if (batch.progress === 100) {
        return 'success';
      }
      return undefined;
    },
    
    // 获取状态文本
    getStatusText(batch) {
      if (batch.total === 0) {
        return '未知'
      }
      if (batch.progress === 100) {
        return '已完成'
      }
      if (batch.processing > 0) {
        return '处理中'
      }
      if (batch.pending === batch.total) {
        return '待处理'
      }
      return '处理中'
    },
    
    // 获取状态类型
    getStatusType(batch) {
      if (batch.total === 0) {
        return 'info'
      }
      if (batch.progress === 100) {
        return 'success'
      }
      if (batch.processing > 0) {
        return 'warning'
      }
      if (batch.pending === batch.total) {
        return 'info'
      }
      return 'warning'
    },
    
    // 判断是否可以处理
    canProcess(batch) {
      return batch.pending > 0 && batch.processing === 0
    },
    
    // 跳转到失败任务页面
    viewFailedTasks() {
      this.$router.push('/failed-tasks')
    },
    
    // 查看处理中的任务
    async viewProcessingTasks() {
      this.processingDialogVisible = true
      await this.fetchProcessingTasks()
    },
    
    // 获取处理中的任务
    async fetchProcessingTasks() {
      this.processingTasksLoading = true
      try {
        const res = await getProcessingTasks()
        if (res.data) {
          this.processingTasks = res.data
          // 重置选中状态
          this.selectedProcessingIds = []
          this.selectedAll = false
        } else {
          this.processingTasks = []
        }
      } catch (error) {
        console.error('获取处理中的任务失败', error)
        this.$message.error('获取处理中的任务失败')
      } finally {
        this.processingTasksLoading = false
      }
    },
    
    // 处理处理中任务表格选择变化
    handleProcessingSelectionChange(val) {
      this.selectedProcessingTasks = val
    },
    
    // 全选/取消全选
    handleSelectAll(val) {
      this.selectedAll = val
      if (val) {
        this.selectedProcessingIds = this.processingTasks.map(task => task.id)
      } else {
        this.selectedProcessingIds = []
      }
    },
    
    // 单个选择
    handleSelectTask(task, val) {
      if (val) {
        if (!this.selectedProcessingIds.includes(task.id)) {
          this.selectedProcessingIds.push(task.id)
        }
      } else {
        this.selectedProcessingIds = this.selectedProcessingIds.filter(id => id !== task.id)
      }
      
      // 更新全选状态
      this.updateSelectedAllState()
    },
    
    // 切换选择
    toggleSelectTask(task) {
      const index = this.selectedProcessingIds.findIndex(id => id === task.id)
      if (index > -1) {
        this.selectedProcessingIds.splice(index, 1)
      } else {
        this.selectedProcessingIds.push(task.id)
      }
      
      // 更新全选状态
      this.updateSelectedAllState()
    },
    
    // 更新全选状态
    updateSelectedAllState() {
      this.selectedAll = this.processingTasks.length > 0 && 
                         this.selectedProcessingIds.length === this.processingTasks.length
    },
    
    // 检查是否被选中
    isSelected(id) {
      return this.selectedProcessingIds.includes(id)
    },
    
    // 重启单个任务
    async handleRestartTask(task) {
      try {
        const res = await restartProcessingTasks([task.id])
        if (res.code === 0) {
          this.$message.success('任务重启成功')
          // 刷新处理中任务列表
          await this.fetchProcessingTasks()
          // 刷新批次进度
          this.fetchBatches()
        } else {
          this.$message.error(`任务重启失败: ${res.message}`)
        }
      } catch (error) {
        console.error('重启任务失败', error)
        this.$message.error('重启任务失败')
      }
    },
    
    // 重启选中的任务
    async handleRestartSelected() {
      if (this.selectedProcessingIds.length === 0) {
        this.$message.warning('请先选择要重启的任务')
        return
      }
      
      try {
        const res = await restartProcessingTasks(this.selectedProcessingIds)
        if (res.code === 0) {
          this.$message.success(`已成功重启 ${res.data.successCount} 个任务`)
          // 刷新处理中任务列表
          await this.fetchProcessingTasks()
          // 刷新批次进度
          this.fetchBatches()
        } else {
          this.$message.error(`任务重启失败: ${res.message}`)
        }
      } catch (error) {
        console.error('重启选中任务失败', error)
        this.$message.error('重启选中任务失败')
      }
    },
    
    // 重启所有处理中的任务
    async handleRestartAll() {
      if (this.processingTasks.length === 0) {
        this.$message.warning('没有需要重启的任务')
        return
      }
      
      try {
        const ids = this.processingTasks.map(task => task.id)
        const res = await restartProcessingTasks(ids)
        if (res.code === 0) {
          this.$message.success(`已成功重启 ${res.data.successCount} 个任务`)
          // 刷新处理中任务列表
          await this.fetchProcessingTasks()
          // 刷新批次进度
          this.fetchBatches()
        } else {
          this.$message.error(`任务重启失败: ${res.message}`)
        }
      } catch (error) {
        console.error('重启所有任务失败', error)
        this.$message.error('重启所有任务失败')
      }
    }
  }
}
</script>

<style scoped>
.task-progress {
  margin: 0 auto;
}
.header-action {
  float: right;
  margin-top: -32px;
}
.batch-card {
  margin-bottom: 20px;
}
.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.batch-header h3 {
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 500px;
}
.batch-info {
  display: flex;
  flex-direction: column;
}
.progress-wrapper {
  margin-bottom: 20px;
}
.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}
.status-counts {
  margin-bottom: 20px;
}
.status-item {
  padding: 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
  text-align: center;
}
.status-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}
.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #606266;
}
.status-item.pending .status-value {
  color: #909399;
}
.status-item.processing .status-value {
  color: #E6A23C;
}
.status-item.completed .status-value {
  color: #67C23A;
}
.status-tag {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}
.dialog-toolbar {
  margin: 15px 0;
}
.task-table {
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}
.task-header {
  display: flex;
  background-color: #f5f7fa;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5;
}
.task-cell {
  padding: 12px 8px;
  font-size: 14px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.task-body {
  max-height: 400px;
  overflow-y: auto;
}
.task-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: background-color 0.3s;
}
.task-row:last-child {
  border-bottom: none;
}
.task-row:hover {
  background-color: #f5f7fa;
}
.row-selected {
  background-color: #e6f7ff;
}
.cell-checkbox {
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cell-id {
  width: 80px;
}
.cell-filename {
  width: 200px;
}
.cell-prompt {
  flex: 1;
  min-width: 150px;
}
.cell-photoname {
  width: 120px;
}
.cell-batchname {
  width: 120px;
}
.cell-createtime {
  width: 180px;
}
.cell-operation {
  width: 80px;
  text-align: center;
}
.empty-tip {
  text-align: center;
  color: #909399;
  padding: 10px;
}
</style> 