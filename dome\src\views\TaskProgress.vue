<template>
  <div class="task-progress">
    <el-card>
      <div slot="header">
        <h2>任务进度</h2>
        <div class="header-action">
          <el-button type="primary" size="small" icon="el-icon-refresh" @click="fetchBatches">刷新</el-button>
        </div>
      </div>
      
      <!-- 批次列表 -->
      <div v-loading="loading">
        <el-empty v-if="batches.length === 0" description="暂无任务，请先导入Excel文件"></el-empty>
        <div v-else>
          <div v-for="batch in batches" :key="batch.batchNo" class="batch-card">
            <el-card shadow="hover">
              <div slot="header" class="batch-header">
                <h3>批次: {{ batch.batchName || batch.batchNo }}</h3>
                <div class="batch-actions">
                  <el-button 
                    type="primary" 
                    size="mini" 
                    @click="handleProcess(batch)"
                    :disabled="!canProcess(batch)">
                    开始处理
                  </el-button>
                  <el-button 
                    type="info" 
                    size="mini" 
                    @click="viewDetails(batch.batchNo)">
                    查看详情
                  </el-button>
                </div>
              </div>
              
              <div class="batch-info">
                <div class="progress-wrapper">
                  <div class="progress-header">
                    <span>处理进度</span>
                    <span>{{ batch.progress || 0 }}%</span>
                  </div>
                  <el-progress 
                    :percentage="batch.progress || 0" 
                    :status="getProgressStatus(batch)">
                  </el-progress>
                </div>
                
                <div class="status-counts">
                  <el-row :gutter="10">
                    <el-col :span="6">
                      <div class="status-item">
                        <div class="status-label">总数</div>
                        <div class="status-value">{{ batch.total || 0 }}</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="status-item pending">
                        <div class="status-label">待处理</div>
                        <div class="status-value">{{ batch.pending || 0 }}</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="status-item processing">
                        <div class="status-label">处理中</div>
                        <div class="status-value">{{ batch.processing || 0 }}</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="status-item completed">
                        <div class="status-label">已完成</div>
                        <div class="status-value">{{ batch.completed || 0 }}</div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
                
                <div class="status-tag">
                  <el-tag :type="getStatusType(batch)" size="medium">
                    {{ getStatusText(batch) }}
                  </el-tag>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getAllBatches, getTaskProgress, processTask } from '@/api/task'

export default {
  name: 'TaskProgress',
  data() {
    return {
      loading: false,
      batches: [],
      // 轮询任务进度的定时器
      progressTimer: null
    }
  },
  created() {
    this.fetchBatches()
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }
  },
  methods: {
    // 获取所有批次
    async fetchBatches() {
      this.loading = true
      try {
        const res = await getAllBatches()
        this.batches = []
        
        if (res.data && res.data.length > 0) {
          // 获取每个批次的详细信息
          const promises = res.data.map(batchNo => this.fetchBatchProgress(batchNo))
          await Promise.all(promises)
          
          // 按进度从低到高排序
          this.batches.sort((a, b) => {
            // 首先按状态排序：待处理 > 处理中 > 已完成
            const statusOrder = { '待处理': 0, '处理中': 1, '已完成': 2 }
            const statusA = this.getStatusText(a)
            const statusB = this.getStatusText(b)
            
            if (statusOrder[statusA] !== statusOrder[statusB]) {
              return statusOrder[statusA] - statusOrder[statusB]
            }
            
            // 其次按进度排序
            return a.progress - b.progress
          })
        }
      } catch (error) {
        console.error('获取批次列表失败', error)
      } finally {
        this.loading = false
      }
      
      // 开始定时轮询进度
      this.startProgressPolling()
    },
    
    // 获取批次进度
    async fetchBatchProgress(batchNo) {
      try {
        const res = await getTaskProgress(batchNo)
        if (res.data) {
          // 查找是否已经存在该批次
          const index = this.batches.findIndex(b => b.batchNo === batchNo)
          if (index > -1) {
            // 更新已存在的批次
            this.$set(this.batches, index, res.data)
          } else {
            // 添加新批次
            this.batches.push(res.data)
          }
        }
      } catch (error) {
        console.error(`获取批次 ${batchNo} 进度失败`, error)
      }
    },
    
    // 开始轮询进度
    startProgressPolling() {
      // 先清除可能存在的定时器
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
      }
      
      // 设置新的定时器，每5秒轮询一次
      this.progressTimer = setInterval(() => {
        if (this.batches.length > 0) {
          // 只轮询状态不是完成或失败的批次
          const incompleteBatches = this.batches.filter(b => 
            b.progress < 100 && !(b.completed + b.failed === b.total && b.total > 0)
          )
          
          if (incompleteBatches.length > 0) {
            incompleteBatches.forEach(batch => {
              this.fetchBatchProgress(batch.batchNo)
            })
          } else {
            // 如果所有批次都完成了，停止轮询
            clearInterval(this.progressTimer)
            this.progressTimer = null
          }
        }
      }, 5000)
    },
    
    // 开始处理任务
    async handleProcess(batch) {
      try {
        await processTask(batch.batchNo)
        this.$message.success('任务处理已启动')
        
        // 更新该批次的状态
        await this.fetchBatchProgress(batch.batchNo)
        
        // 确保轮询正在进行
        this.startProgressPolling()
      } catch (error) {
        console.error('启动任务处理失败', error)
      }
    },
    
    // 查看任务详情
    viewDetails(batchNo) {
      this.$router.push(`/task-detail/${batchNo}`)
    },
    
    // 获取进度状态
    getProgressStatus(batch) {
      if (batch.failed > 0) {
        return 'exception';
      }
      if (batch.progress === 100) {
        return 'success';
      }
      return undefined;
    },
    
    // 获取状态文本
    getStatusText(batch) {
      if (batch.total === 0) {
        return '未知'
      }
      if (batch.progress === 100) {
        return '已完成'
      }
      if (batch.processing > 0) {
        return '处理中'
      }
      if (batch.pending === batch.total) {
        return '待处理'
      }
      return '处理中'
    },
    
    // 获取状态类型
    getStatusType(batch) {
      if (batch.total === 0) {
        return 'info'
      }
      if (batch.progress === 100) {
        return 'success'
      }
      if (batch.processing > 0) {
        return 'warning'
      }
      if (batch.pending === batch.total) {
        return 'info'
      }
      return 'warning'
    },
    
    // 判断是否可以处理
    canProcess(batch) {
      return batch.pending > 0 && batch.processing === 0
    }
  }
}
</script>

<style scoped>
.task-progress {
  margin: 0 auto;
}
.header-action {
  float: right;
  margin-top: -32px;
}
.batch-card {
  margin-bottom: 20px;
}
.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.batch-header h3 {
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 500px;
}
.batch-info {
  display: flex;
  flex-direction: column;
}
.progress-wrapper {
  margin-bottom: 20px;
}
.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}
.status-counts {
  margin-bottom: 20px;
}
.status-item {
  padding: 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
  text-align: center;
}
.status-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}
.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #606266;
}
.status-item.pending .status-value {
  color: #909399;
}
.status-item.processing .status-value {
  color: #E6A23C;
}
.status-item.completed .status-value {
  color: #67C23A;
}
.status-tag {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}
</style> 