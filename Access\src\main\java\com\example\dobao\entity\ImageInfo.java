package com.example.dobao.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 图片信息实体类
 */
@Data
@NoArgsConstructor
public class ImageInfo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 图片名称
     */
    private String imageName;

    /**
     * 图片路径（存储在服务器上的路径）
     */
    private String imagePath;

    /**
     * 图片类型（PNG/JPG）
     */
    private String imageType;

    /**
     * 图片大小（字节）
     */
    private Long imageSize;

    /**
     * 是否为密封线图片：0-否，1-是
     */
    private Boolean isSealLine;

    /**
     * 密封线配置信息（JSON格式）
     */
    private String sealLineConfig;

    /**
     * Base64编码内容（用于传输）
     */
    private String base64Content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}