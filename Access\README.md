# 豆包API批量调用系统

本项目是一个基于Spring Boot的豆包大模型API批量调用系统，支持通过Excel批量导入任务，多线程调用豆包API，生成Word文档。

## 主要功能

1. **图片管理**
   - 上传、查询、删除图片
   - 支持PNG和JPG格式

2. **Excel批量导入**
   - 支持Excel文件导入任务
   - Excel格式要求包含proment(提示词)、photoname(图片名)、filename(生成文件名)列

3. **多线程处理**
   - 使用线程池并发处理任务
   - 避免单线程处理导致的执行时间过长

4. **任务管理**
   - 查看任务执行进度
   - 查询任务执行结果
   - 下载生成的Word文档

## 技术栈

- 后端：Spring Boot 2.6.13 + MyBatis + MySQL
- 数据解析：EasyExcel
- Word生成：Apache POI
- 并发处理：Spring Async + 线程池

## 安装部署

### 环境要求

- JDK 1.8+
- MySQL 5.7+
- Maven 3.6+

### 数据库初始化

1. 创建数据库和表：
   ```sql
   source src/main/resources/db/init.sql
   ```

### 配置文件

1. 修改 `application.yml` 中的数据库连接信息和豆包API配置。

### 打包部署

```bash
# 打包
mvn clean package

# 启动
java -jar target/dobao-0.0.1-SNAPSHOT.jar
```

## API接口说明

### 图片管理

- 上传图片：`POST /api/images/upload`
- 获取所有图片：`GET /api/images/list`
- 删除图片：`DELETE /api/images/{id}`

### 任务管理

- 导入Excel：`POST /api/tasks/import`
- 开始处理任务：`POST /api/tasks/process/{batchNo}`
- 获取处理进度：`GET /api/tasks/progress/{batchNo}`
- 获取批次任务列表：`GET /api/tasks/list/{batchNo}`
- 获取所有批次：`GET /api/tasks/batches`
- 下载Word文档：`GET /api/tasks/download/{id}`

## Excel文件格式

Excel文件中必须包含以下列：
- proment：发送给豆包的提问内容
- photoname：前端图片管理的图片名，可选
- filename：生成的Word文件名称

## 注意事项

1. 豆包API调用有频率限制，请合理设置线程池大小
2. 大文件导入可能需要调整服务器内存配置
3. 请确保MySQL和服务器有足够的存储空间 