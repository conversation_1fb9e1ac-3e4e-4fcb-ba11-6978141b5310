<template>
  <div class="task-detail">
    <el-card>
      <div slot="header">
        <el-page-header @back="goBack" :content="`批次: ${batchNo}`"></el-page-header>
        <div class="header-action">
          <el-button type="primary" size="small" icon="el-icon-refresh" @click="fetchTasks">刷新</el-button>
        </div>
      </div>

      <!-- 批次进度 -->
      <el-card class="progress-card" shadow="hover" v-if="progress">
        <div class="progress-info">
          <div class="progress-wrapper">
            <div class="progress-header">
              <span>处理进度</span>
              <span>{{ progress.progress || 0 }}%</span>
            </div>
            <el-progress
              :percentage="progress.progress || 0"
              :status="getProgressStatus(progress)">
            </el-progress>
          </div>

          <div class="status-counts">
            <el-row :gutter="10">
              <el-col :span="6">
                <div class="status-item">
                  <div class="status-label">总数</div>
                  <div class="status-value">{{ progress.total || 0 }}</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="status-item pending">
                  <div class="status-label">待处理</div>
                  <div class="status-value">{{ progress.pending || 0 }}</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="status-item processing">
                  <div class="status-label">处理中</div>
                  <div class="status-value">{{ progress.processing || 0 }}</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="status-item completed">
                  <div class="status-label">已完成</div>
                  <div class="status-value">{{ progress.completed || 0 }}</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="batch-action" v-if="canProcess(progress)">
            <el-button type="primary" @click="handleProcess">开始处理</el-button>
          </div>
        </div>
      </el-card>

      <!-- Word文档列表 -->
      <el-card class="word-docs-card" shadow="hover">
        <div slot="header" class="word-docs-header">
          <span>Word文档列表</span>
          <div class="header-actions">
            <el-button type="text" size="small" @click="showRawData = !showRawData">
              {{ showRawData ? '隐藏原始数据' : '显示原始数据' }}
            </el-button>
            <span class="debug-info">列表项数: {{completedTasks ? completedTasks.length : 0}} / 总数: {{tasks ? tasks.length : 0}}</span>
          </div>
        </div>

        <!-- 调试视图 -->
<!--        <div class="debug-panel">-->
<!--          <p>任务状态为2的项目数: {{tasks ? tasks.filter(t => t && Number(t.status) === 2).length : 0}}</p>-->
<!--          <p>任务数据类型: {{tasks && tasks.length > 0 ? typeof tasks[0].status : '无数据'}}</p>-->
<!--          <p>第一个任务状态: {{tasks && tasks.length > 0 ? tasks[0].status : '无数据'}}</p>-->
<!--          <p>第一个任务ID: {{tasks && tasks.length > 0 ? tasks[0].id : '无数据'}}</p>-->
<!--          <p>第一个任务文件名: {{tasks && tasks.length > 0 ? tasks[0].fileName : '无数据'}}</p>-->
<!--          <el-button type="primary" size="small" @click="testWithMockData">使用测试数据</el-button>-->
<!--        </div>-->

        <!-- 原始数据显示 -->
        <div v-if="showRawData" class="raw-data-panel">
          <h4>原始任务数据:</h4>
          <pre>{{ JSON.stringify(tasks, null, 2) }}</pre>
        </div>

        <!-- 直接渲染所有任务 - Element UI表格 -->
        <div v-if="completedTasks && completedTasks.length > 0" class="el-table-container">
          <!-- 批量操作按钮 -->
          <div class="batch-actions" v-if="completedTasks.length > 0">
            <el-tooltip content="下载的文件将按照图片名称分组，没有图片名称的文件将放在根目录" placement="top">
              <el-button type="primary" size="small" 
                @click="handleBatchDownload">批量下载全部</el-button>
            </el-tooltip>
            <el-button type="danger" size="small" 
              @click="confirmBatchDelete">批量删除全部</el-button>
          </div>

          <el-table
            :data="completedTasks"
            border
            style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
              prop="id"
              label="ID"
              width="80">
            </el-table-column>
            <el-table-column
              prop="fileName"
              label="文件名"
              show-overflow-tooltip>
            </el-table-column>
            <el-table-column
              prop="status"
              label="状态"
              width="100">
              <template slot-scope="scope">
                <el-tag :type="getTaskStatusType(scope.row.status)">
                  {{ getTaskStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="200"
              align="center">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  size="mini"
                  :disabled="Number(scope.row.status) !== 2"
                  @click="downloadWord(scope.row)">
                  下载
                </el-button>
                <el-button
                  type="danger"
                  size="mini"
                  :disabled="Number(scope.row.status) !== 2"
                  @click="confirmDeleteWord(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 简单HTML表格备用方案 -->
        <div v-if="completedTasks && completedTasks.length > 0" class="simple-table-container">
          <h4>简单表格渲染:</h4>
          <table class="simple-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>文件名</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="task in completedTasks" :key="task.id">
                <td>{{ task.id }}</td>
                <td>{{ task.fileName }}</td>
                <td>{{ getTaskStatusText(task.status) }}</td>
                <td>
                  <button @click="downloadWord(task)">下载</button>
                  <button @click="confirmDeleteWord(task)">删除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-else class="no-data-message">
          <p>没有已完成的任务可显示</p>
          <el-button type="primary" size="small" @click="testWithMockData">使用测试数据</el-button>
        </div>
      </el-card>

      <!-- 任务列表 -->
      <div v-loading="loading" class="task-list">
        <h3>所有任务列表</h3>

        <!-- Element UI表格 -->
        <el-table
          :data="tasks"
          border
          style="width: 100%"
          :default-sort="{prop: 'id', order: 'ascending'}"
          row-key="id">
          <el-table-column
            prop="id"
            label="ID"
            width="80"
            sortable>
          </el-table-column>
          <el-table-column
            prop="fileName"
            label="文件名"
            width="180"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            prop="photoName"
            label="图片名"
            width="180"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            prop="prompt"
            label="提示词"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            label="状态"
            width="100"
            align="center">
            <template slot-scope="scope">
              <el-tag :type="getTaskStatusType(scope.row.status)">
                {{ getTaskStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="150"
            align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                :disabled="Number(scope.row.status) !== 2"
                @click="downloadWord(scope.row)">
                下载文档
              </el-button>
              <el-button
                type="text"
                @click="showDetail(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 简单HTML表格备用方案 -->
        <div class="simple-table-container">
          <h4>简单表格渲染 - 所有任务:</h4>
          <table class="simple-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>文件名</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="task in tasks" :key="task.id">
                <td>{{ task.id }}</td>
                <td>{{ task.fileName }}</td>
                <td>{{ getTaskStatusText(task.status) }}</td>
                <td>
                  <button @click="showDetail(task)">详情</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </el-card>

    <!-- 任务详情对话框 -->
    <el-dialog
      title="任务详情"
      :visible.sync="detailDialogVisible"
      width="60%">
      <div v-if="currentTask">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="ID">{{ currentTask.id }}</el-descriptions-item>
          <el-descriptions-item label="文件名">{{ currentTask.fileName }}</el-descriptions-item>
          <el-descriptions-item label="图片名">{{ currentTask.photoName || '无' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getTaskStatusType(currentTask.status)">
              {{ getTaskStatusText(currentTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="提示词">
            <div class="prompt-text">{{ currentTask.prompt }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="生成内容" v-if="currentTask.content">
            <div class="content-text">{{ currentTask.content }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="错误信息" v-if="currentTask.errorMsg">
            <div class="error-text">{{ currentTask.errorMsg }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <div class="dialog-footer" slot="footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </div>
    </el-dialog>
    
    <!-- 自定义压缩包名称对话框 -->
    <el-dialog
      title="自定义压缩包名称"
      :visible.sync="zipNameDialogVisible"
      width="30%">
      <el-form>
        <el-form-item label="压缩包名称">
          <el-input 
            v-model="customZipName" 
            placeholder="请输入压缩包名称" 
            clearable>
          </el-input>
          <span class="zip-name-tip">不需要输入.zip后缀，系统会自动添加</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="zipNameDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDownloadWithName">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTaskList, getTaskProgress, processTask, downloadWord, deleteWord, physicalDeleteWord, batchDeleteWords, batchDownloadWords, batchPhysicalDeleteWords } from '@/api/task'

export default {
  name: 'TaskDetail',
  data() {
    return {
      batchNo: '',
      loading: false,
      tasks: [],
      progress: null,
      progressTimer: null,
      detailDialogVisible: false,
      currentTask: null,
      showRawData: false, // 控制是否显示原始数据
      selectedRows: [], // 选中的行
      // 添加自定义压缩包名称相关数据
      zipNameDialogVisible: false,
      customZipName: '',
      // 添加模拟数据，用于测试列表显示
      mockData: [
        {
          id: 1001,
          fileName: '模拟文档1.docx',
          status: 2,
          photoName: '测试图片1.jpg',
          content: '模拟内容1'
        },
        {
          id: 1002,
          fileName: '模拟文档2.docx',
          status: 2,
          photoName: '测试图片2.jpg',
          content: '模拟内容2'
        },
        {
          id: 1003,
          fileName: '模拟文档3.docx',
          status: 2,
          photoName: '测试图片3.jpg',
          content: '模拟内容3'
        }
      ]
    }
  },
  computed: {
    // 获取已完成的任务列表（用于Word文档列表）
    completedTasks() {
      console.log("当前任务列表:", this.tasks);

      if (!this.tasks || !Array.isArray(this.tasks)) {
        console.error("任务列表不存在或不是数组");
        return [];
      }

      // 直接过滤状态为2(已完成)的任务
      const filtered = this.tasks.filter(task => task && Number(task.status) === 2);

      console.log("过滤后的列表数量:", filtered.length);
      return filtered;
    }
  },
   created() {
    this.batchNo = this.$route.params.batchNo
    this.fetchData()

    // 开始定时轮询进度
    this.startProgressPolling()
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }
  },
  methods: {
    // 获取数据
    fetchData() {
      this.fetchProgress()
      this.fetchTasks()
    },

    // 获取批次进度
    async fetchProgress() {
      try {
        const res = await getTaskProgress(this.batchNo)
        if (res.data) {
          this.progress = res.data
        }
      } catch (error) {
        console.error('获取进度失败', error)
      }
    },

    // 获取批次任务列表
    async fetchTasks() {
      this.loading = true
      try {
        const res = await getTaskList(this.batchNo)
        console.log("API返回数据:", res)

        // 根据API返回结构 {code: 0, data: Array(19), message: '获取成功'}
        if (res && res.code === 0 && Array.isArray(res.data)) {
          // 直接使用res.data数组
          this.tasks = [...res.data]; // 使用展开运算符创建新数组，避免引用问题
        }
        // 兼容其他可能的数据结构
        else if (res && res.data) {
          // 如果res.data本身是数组
          if (Array.isArray(res.data)) {
            this.tasks = [...res.data];
          }
          // 如果res.data包含code和data字段（嵌套结构）
          else if (res.data.code === 0 && Array.isArray(res.data.data)) {
            this.tasks = [...res.data.data];
          }
          // 如果res.data是JSON字符串
          else if (typeof res.data === 'string') {
            try {
              const parsedData = JSON.parse(res.data);
              if (Array.isArray(parsedData)) {
                this.tasks = [...parsedData];
              } else if (parsedData.data && Array.isArray(parsedData.data)) {
                this.tasks = [...parsedData.data];
              }
            } catch (e) {
              console.error("解析JSON字符串失败:", e);
            }
          }
          // 如果都不是，记录错误
          else {
            console.error("无法识别的数据结构:", res.data);
            this.$message.error("解析服务器数据失败");
          }
        }

        // 确保tasks是一个数组
        if (!Array.isArray(this.tasks)) {
          this.tasks = [];
        }

        console.log("最终使用的任务数据:", this.tasks);
      } catch (error) {
        console.error('获取任务列表失败', error)
        this.$message.error('获取任务列表失败')
      } finally {
        this.loading = false
      }
    },

    // 开始轮询进度
    startProgressPolling() {
      // 先清除可能存在的定时器
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
      }

      // 设置新的定时器，每5秒轮询一次
      this.progressTimer = setInterval(() => {
        // 只在处理中状态轮询
        if (this.progress && this.progress.processing > 0) {
          this.fetchProgress()
          this.fetchTasks()
        } else if (this.progress && this.progress.progress === 100) {
          // 如果进度已完成，停止轮询
          clearInterval(this.progressTimer)
          this.progressTimer = null
        }
      }, 5000)
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 开始处理任务
    async handleProcess() {
      try {
        await processTask(this.batchNo)
        this.$message.success('任务处理已启动')

        // 更新进度和任务列表
        await this.fetchProgress()
        await this.fetchTasks()

        // 确保轮询正在进行
        this.startProgressPolling()
      } catch (error) {
        console.error('启动任务处理失败', error)
        this.$message.error('启动任务处理失败')
      }
    },

    // 显示任务详情
    showDetail(task) {
      this.currentTask = task
      this.detailDialogVisible = true
    },

    // 下载Word文档
    async downloadWord(task) {
      try {
        const response = await downloadWord(task.id)

        // 创建blob链接并下载
        const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 尝试从响应头获取文件名，如果没有则使用任务中的文件名
        const contentDisposition = response.headers['content-disposition']
        let filename = task.fileName
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+?)"/)
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1]
          }
        }

        // 确保文件名有.docx后缀
        if (!filename.toLowerCase().endsWith('.docx')) {
          filename += '.docx'
        }

        link.setAttribute('download', filename)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('下载成功')
      } catch (error) {
        console.error('下载文档失败', error)
        this.$message.error('下载失败')
      }
    },

    // 确认删除Word文档
    confirmDeleteWord(task) {
      this.$confirm(`确定要删除文件 "${task.fileName}" 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$confirm('是否同时从数据库中彻底删除记录？', '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        }).then(() => {
          // 物理删除（文件+数据库记录）
          this.physicalDeleteWord(task);
        }).catch(() => {
          // 仅删除文件，保留数据库记录
          this.deleteWord(task);
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },

    // 删除Word文档（仅删除文件，保留数据库记录）
    async deleteWord(task) {
      try {
        // 调用删除API
        const res = await deleteWord(task.id);
        console.log(res)
        if (res.code === 0) {
          this.$message.success('删除成功');
          // 刷新任务列表
          this.fetchTasks();
        } else {
          this.$message.error(res.message || '删除失败');
        }
      } catch (error) {
        console.error('删除文档失败', error);
        this.$message.error('删除失败');
      }
    },
    
    // 物理删除Word文档和数据库记录
    async physicalDeleteWord(task) {
      try {
        // 调用物理删除API
        const res = await physicalDeleteWord(task.id);
        console.log(res)
        if (res.code === 0) {
          this.$message.success('文件和数据库记录已彻底删除');
          // 刷新任务列表
          this.fetchTasks();
        } else {
          this.$message.error(res.message || '物理删除失败');
        }
      } catch (error) {
        console.error('物理删除文档失败', error);
        this.$message.error('物理删除失败');
      }
    },

    // 获取进度状态
    getProgressStatus(progress) {
      if (progress.failed > 0) {
        return 'exception'
      }
      if (progress.progress === 100) {
        return 'success'
      }
      return undefined
    },

    // 判断是否可以处理
    canProcess(progress) {
      return progress.pending > 0 && progress.processing === 0
    },

    // 获取任务状态文本
    getTaskStatusText(status) {
      const statusMap = {
        0: '待处理',
        1: '处理中',
        2: '已完成',
        3: '失败'
      }
      return statusMap[status] || '未知'
    },

    // 获取任务状态类型
    getTaskStatusType(status) {
      const typeMap = {
        0: 'info',
        1: 'warning',
        2: 'success',
        3: 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 使用测试数据
    testWithMockData() {
      // 使用模拟数据替换任务列表
      this.tasks = JSON.parse(JSON.stringify(this.mockData));
      console.log("使用模拟数据:", this.tasks);

      // 强制更新视图
      this.$nextTick(() => {
        console.log("视图更新后的任务列表:", this.tasks);
        console.log("视图更新后的已完成任务:", this.completedTasks);
        this.$forceUpdate();
        this.$message.success('已加载测试数据');
      });
    },

    // 表格选择项变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 批量下载
    async handleBatchDownload() {
      if (this.completedTasks.length === 0) {
        this.$message.warning('没有可下载的文件');
        return;
      }

      // 生成默认名称作为建议
      this.generateDefaultZipName();
        
      // 显示自定义名称对话框
      this.zipNameDialogVisible = true;
    },
    
    // 生成默认的压缩包名称
    generateDefaultZipName() {
      // 从任务数据构建文件名
          const photoGroups = new Map();
          
          // 统计photoname出现次数
          this.completedTasks.forEach(task => {
            if (task.photoName && task.photoName.trim()) {
              const photoName = task.photoName.trim();
              photoGroups.set(photoName, (photoGroups.get(photoName) || 0) + 1);
            }
          });
          
          // 构建文件名
          if (photoGroups.size > 0) {
            const parts = [];
            let count = 0;
            
            for (const [name, size] of photoGroups.entries()) {
              parts.push(`${name}${size}篇`);
              count++;
              if (count >= 3) {
                parts.push('等');
                break;
              }
            }
            
            if (parts.length > 0) {
          this.customZipName = parts.join('_');
          return;
        }
      }
      
      // 如果无法从图片名生成，则使用批次号+数量
      this.customZipName = `${this.batchNo}_${this.completedTasks.length}篇文档`;
    },
    
    // 使用自定义名称确认下载
    async confirmDownloadWithName() {
      // 检查名称是否为空
      if (!this.customZipName.trim()) {
        this.$message.warning('压缩包名称不能为空');
        return;
      }
      
      // 关闭对话框
      this.zipNameDialogVisible = false;
      
      try {
        // 显示下载进度提示
        this.$message({
          message: `正在打包 ${this.completedTasks.length} 个文件，按图片名称进行分组...`,
          type: 'info',
          duration: 0,
          showClose: true
        });

        // 获取所有已完成任务的ID
        const ids = this.completedTasks.map(row => row.id);
        
        // 调用批量下载API
        const response = await batchDownloadWords(ids);
        
        // 准备下载文件名，添加.zip后缀
        let filename = this.customZipName.trim();
        if (!filename.toLowerCase().endsWith('.zip')) {
          filename += '.zip';
        }
        
        // 创建下载链接
        const blob = new Blob([response.data], { type: response.headers['content-type'] || 'application/zip' });
        const url = window.URL.createObjectURL(blob);
        
        // 使用a标签下载，明确指定文件名
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', filename);
        console.log('最终使用的下载文件名:', filename);
        
        // 将链接添加到文档并触发点击
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        // 关闭所有消息提示
        this.$message.closeAll();
        this.$message.success(`${this.completedTasks.length} 个文件下载成功，已按图片名称分组`);
      } catch (error) {
        this.$message.closeAll();
        console.error('批量下载失败', error);
        this.$message.error('批量下载失败');
      }
    },

    // 确认批量删除
    confirmBatchDelete() {
      if (this.completedTasks.length === 0) {
        this.$message.warning('没有可删除的文件');
        return;
      }

      this.$confirm(`确定要删除全部 ${this.completedTasks.length} 个文件吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$confirm('是否同时从数据库中彻底删除记录？', '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        }).then(() => {
          // 物理删除（文件+数据库记录）
          this.handleBatchPhysicalDelete();
        }).catch(() => {
          // 仅删除文件，保留数据库记录
          this.handleBatchDelete();
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },

    // 执行批量删除（仅删除文件，保留数据库记录）
    async handleBatchDelete() {
      try {
        // 获取所有已完成任务的ID
        const ids = this.completedTasks.map(row => row.id);
        
        // 调用批量删除API
        const res = await batchDeleteWords(ids);
        
        if (res.code === 0) {
          this.$message.success(res.message || '批量删除成功');
          
          // 如果有错误详情，显示错误信息
          if (res.errorDetail) {
            this.$notify({
              title: '部分删除失败',
              message: res.errorDetail,
              type: 'warning',
              duration: 5000
            });
          }
          
          // 刷新任务列表
          this.fetchTasks();
        } else {
          this.$message.error(res.message || '批量删除失败');
        }
      } catch (error) {
        console.error('批量删除失败', error);
        this.$message.error('批量删除失败');
      }
    },
    
    // 执行批量物理删除（删除文件和数据库记录）
    async handleBatchPhysicalDelete() {
      try {
        // 获取所有已完成任务的ID
        const ids = this.completedTasks.map(row => row.id);
        
        // 调用批量物理删除API
        const res = await batchPhysicalDeleteWords(ids);
        
        if (res.code === 0) {
          this.$message.success(res.message || '批量物理删除成功');
          
          // 如果有错误详情，显示错误信息
          if (res.errorDetail) {
            this.$notify({
              title: '部分物理删除失败',
              message: res.errorDetail,
              type: 'warning',
              duration: 5000
            });
          }
          
          // 刷新任务列表
          this.fetchTasks();
        } else {
          this.$message.error(res.message || '批量物理删除失败');
        }
      } catch (error) {
        console.error('批量物理删除失败', error);
        this.$message.error('批量物理删除失败');
      }
    }
  }
}
</script>

<style scoped>
.task-detail {
  margin: 0 auto;
}
.header-action {
  float: right;
  margin-top: -32px;
}
.progress-card {
  margin-bottom: 20px;
}
.progress-info {
  display: flex;
  flex-direction: column;
}
.progress-wrapper {
  margin-bottom: 20px;
}
.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}
.status-counts {
  margin-bottom: 20px;
}
.status-item {
  padding: 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
  text-align: center;
}
.status-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}
.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #606266;
}
.status-item.pending .status-value {
  color: #909399;
}
.status-item.processing .status-value {
  color: #E6A23C;
}
.status-item.completed .status-value {
  color: #67C23A;
}
.batch-action {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.word-docs-card {
  margin-bottom: 20px;
}
.word-docs-header {
  font-weight: bold;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
}
.debug-info {
  color: #909399;
  font-size: 12px;
  font-weight: normal;
}
.no-word-files {
  padding: 20px 0;
  text-align: center;
}
.task-list {
  margin-top: 20px;
}
.prompt-text, .content-text, .error-text {
  white-space: pre-wrap;
  overflow-wrap: break-word;
  max-height: 200px;
  overflow: auto;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
.error-text {
  color: #F56C6C;
}
.debug-panel {
  background-color: #f8f8f8;
  padding: 8px 12px;
  margin-bottom: 12px;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}
.debug-panel p {
  margin: 4px 0;
  font-size: 12px;
  color: #606266;
}
.no-data-message {
  text-align: center;
  padding: 30px 0;
  color: #909399;
}
.no-data-message p {
  margin-bottom: 15px;
  font-size: 14px;
}
.raw-data-panel {
  margin: 10px 0 20px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
}
.raw-data-panel h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #606266;
}
.raw-data-panel pre {
  max-height: 300px;
  overflow: auto;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.header-actions {
  display: flex;
  align-items: center;
}
.simple-table-container {
  margin: 20px 0;
}
.simple-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #dcdfe6;
  margin-top: 10px;
}
.simple-table th, .simple-table td {
  padding: 12px;
  text-align: left;
  border: 1px solid #dcdfe6;
}
.simple-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
}
.simple-table tr:hover {
  background-color: #f5f7fa;
}
.simple-table button {
  margin-right: 5px;
  padding: 5px 10px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}
.simple-table button:last-child {
  background-color: #f56c6c;
}
.el-table-container, .simple-table-container {
  margin-bottom: 20px;
}
.batch-actions {
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}
.zip-name-tip {
  display: block;
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}
</style>