package com.example.dobao.mapper;

import com.example.dobao.entity.ImageInfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 图片信息Mapper
 */
@Mapper
public interface ImageInfoMapper {

    /**
     * 保存图片信息
     */
    @Insert("INSERT INTO image_info(image_name, image_path, image_type, image_size, is_seal_line, seal_line_config, create_time, update_time) " +
            "VALUES(#{imageName}, #{imagePath}, #{imageType}, #{imageSize}, #{isSealLine}, #{sealLineConfig}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int save(ImageInfo imageInfo);

    /**
     * 根据ID查询图片信息
     */
    @Select("SELECT * FROM image_info WHERE id = #{id}")
    ImageInfo findById(Long id);

    /**
     * 根据图片名称查询图片信息
     */
    @Select("SELECT * FROM image_info WHERE image_name = #{imageName}")
    ImageInfo findByImageName(String imageName);

    /**
     * 查询所有图片信息
     */
    @Select("SELECT * FROM image_info ORDER BY create_time DESC")
    List<ImageInfo> findAll();

    /**
     * 更新图片信息
     */
    @Update("UPDATE image_info SET image_name = #{imageName}, image_path = #{imagePath}, " +
            "image_type = #{imageType}, image_size = #{imageSize}, is_seal_line = #{isSealLine}, " +
            "seal_line_config = #{sealLineConfig}, update_time = NOW() " +
            "WHERE id = #{id}")
    int update(ImageInfo imageInfo);

    /**
     * 删除图片信息
     */
    @Delete("DELETE FROM image_info WHERE id = #{id}")
    int deleteById(Long id);
} 