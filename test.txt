1.前端技术栈 HTML+CSS+vue2
2.后端技术栈
框架体系：Springboot+MyBatis+MySQL+EasyExcel
释：电脑开机内存占用高，如果使用easyPoi这类Excel解析工具担心爆内存，请使用EasyExcel解析上传文件。

完成批量化调用豆包API对话

管理图片
新增图片：出一个弹窗，填写图片名称，选择图片文件（支持PNG和JPG格式），提交后存储至MySQL

查询执行结果
对大模型给的word文件进行查询和下载（从MySQL下载到本地）
介绍：豆包大模型回复内容需要时间，假设Excel中含有100条命令，需要知道这些任务是否都执行完毕

excel文件格式
proment发送给豆包提问，photoname是前端图片管理的那些图片，你可以把图片一起发给豆包让它拼接，也可以后期拼接，但是每一条任务需要根据对应的photoname拼接。
生成的word文件名称使用filename

多线程的调用
由于大模型执行较慢，单线程会导致excel文件执行过程相当漫长，需要使用多线程加快处理进度。

word文件的格式及排版样式
字体：微软雅黑



根据需求文档，我将为您整理项目需求：
技术栈
前端
HTML+CSS+Vue2
后端
框架体系：Springboot+MyBatis+MySQL+EasyExcel
注意：使用EasyExcel而非easyPoi解析Excel，避免内存占用过高
功能需求
1. 批量调用豆包API对话
通过Excel文件批量发送提问到豆包大模型
需使用多线程处理，加快处理速度
2. 图片管理
新增图片功能：弹窗填写图片名称，选择图片文件
支持PNG和JPG格式
图片存储至MySQL
3. 查询执行结果
查询大模型处理任务的完成状态
下载生成的Word文件（从MySQL到本地）
4. Excel文件格式要求
包含proment字段：发送给豆包的提问内容
包含photoname字段：关联前端图片管理的图片
包含filename字段：用于生成的word文件命名
5. Word文件格式要求
字体：微软雅黑
需要合理排版
6. 性能要求
使用多线程调用API，提高处理效率
避免内存占用过高的问题
这个项目主要是一个批量处理系统，核心功能是将Excel中的多条命令通过多线程方式发送给豆包大模型API，并将结果生成为Word文档，同时需要管理相关的图片资源和查询任务执行状态。