package com.example.dobao.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 简单的API测试，直接执行main方法测试
 */
public class SimpleApiTest {
    private static final String API_URL = "https://ark.cn-beijing.volces.com";
    private static final String API_KEY = "5ee3c0ba-26fe-4284-a263-347e9d5577bc"; // 请替换为你的API密钥
    private static final String MODEL_NAME = "doubao-lite"; // 使用基础模型进行测试

    public static void main(String[] args) {
        System.out.println("开始测试豆包API调用...");
        
        OkHttpClient client = new OkHttpClient.Builder()
                .build();

        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("model", MODEL_NAME);
        
        List<Map<String, String>> messages = new ArrayList<>();
        Map<String, String> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", "你好，请简单介绍一下自己");
        messages.add(message);
        
        requestBody.put("messages", messages);
        requestBody.put("temperature", 0.7);
        requestBody.put("max_tokens", 2000);
        
        System.out.println("请求参数: " + requestBody.toJSONString());

        // 构建HTTP请求
        RequestBody body = RequestBody.create(
                MediaType.parse("application/json; charset=utf-8"),
                requestBody.toJSONString()
        );
        
        Request request = new Request.Builder()
                .url(API_URL)
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "application/json")
                .addHeader("Authorization", "Bearer " + API_KEY)
                .post(body)
                .build();

        try {
            System.out.println("发送请求到: " + API_URL);
            Response response = client.newCall(request).execute();
            
            if (response.isSuccessful()) {
                ResponseBody responseBody = response.body();
                if (responseBody != null) {
                    String responseString = responseBody.string();
                    System.out.println("API调用成功，响应: " + responseString);
                    
                    // 解析响应中的content内容
                    try {
                        JSONObject jsonResponse = JSON.parseObject(responseString);
                        if (jsonResponse.containsKey("choices") && !jsonResponse.getJSONArray("choices").isEmpty()) {
                            String content = jsonResponse.getJSONArray("choices")
                                    .getJSONObject(0)
                                    .getJSONObject("message")
                                    .getString("content");
                            System.out.println("\n内容: " + content);
                        }
                    } catch (Exception e) {
                        System.out.println("解析响应内容失败: " + e.getMessage());
                    }
                } else {
                    System.out.println("API调用成功，但响应体为空");
                }
            } else {
                System.out.println("API调用失败，状态码: " + response.code());
                if (response.body() != null) {
                    System.out.println("错误信息: " + response.body().string());
                }
            }
        } catch (IOException e) {
            System.out.println("API调用异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试完成");
    }
} 