<template>
  <div class="task-management">
    <el-card>
      <div slot="header">
        <h2>任务管理</h2>
      </div>
      
      <!-- 导入Excel表单 -->
      <el-form :model="uploadForm" :rules="uploadRules" ref="uploadForm" label-width="100px" class="import-form">
        <el-form-item label="批次名称" prop="batchName">
          <el-input v-model="uploadForm.batchName" placeholder="请输入批次名称（可选）"></el-input>
        </el-form-item>
        <el-form-item label="Excel文件" prop="file">
          <el-upload
            class="excel-uploader"
            action="#"
            :http-request="handleUpload"
            :show-file-list="true"
            :limit="1"
            :file-list="fileList"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            accept=".xlsx,.xls">
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <div class="el-upload__tip" slot="tip">只能上传Excel文件，且文件须包含proment、photoname、filename列</div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitUpload" :loading="uploading">导入并创建任务</el-button>
          <el-button @click="downloadTemplate">下载模板</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 批次处理队列状态 -->
      <el-divider content-position="left">处理队列状态</el-divider>
      <div v-loading="queueLoading" class="queue-status">
        <el-alert
          v-if="queueStatus.isProcessing"
          type="warning"
          title="批次处理中"
          description="系统正在处理队列中的批次，新导入的批次将按顺序处理"
          show-icon
          :closable="false">
        </el-alert>
        <el-alert
          v-else
          type="success"
          title="队列空闲"
          description="当前没有批次正在处理，新导入的批次将立即处理"
          show-icon
          :closable="false">
        </el-alert>
        
        <div v-if="queueStatus.queuedBatches && queueStatus.queuedBatches.length > 0" class="queue-list">
          <h4>等待处理的批次 ({{ queueStatus.queueSize || 0 }}个):</h4>
          <el-table :data="queueStatus.queuedBatches" border style="width: 100%">
            <el-table-column type="index" label="队列位置" width="80"></el-table-column>
            <el-table-column prop="batchNo" label="批次号" width="240"></el-table-column>
            <el-table-column prop="batchName" label="批次名称" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.batchName || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="taskCount" label="任务数量" width="100" align="center">
              <template slot-scope="scope">
                <el-tag type="info">{{ scope.row.taskCount }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <!-- 表格说明 -->
      <el-divider content-position="left">Excel文件格式说明</el-divider>
      <div class="format-description">
        <p>Excel文件必须包含以下列（请注意列名大小写）：</p>
        <el-table :data="excelColumns" border style="width: 100%">
          <el-table-column prop="name" label="列名" width="180"></el-table-column>
          <el-table-column prop="description" label="说明"></el-table-column>
          <el-table-column prop="required" label="是否必填" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.required ? 'danger' : 'info'">
                {{ scope.row.required ? '必填' : '选填' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 任务列表 -->
      <el-divider content-position="left">导入历史</el-divider>
      <div v-loading="loading">
        <el-empty v-if="batches.length === 0" description="暂无任务，请导入Excel文件"></el-empty>
        <div v-else>
          <el-table :data="batches" border style="width: 100%">
            <el-table-column prop="batchNo" label="批次号" width="240"></el-table-column>
            <el-table-column prop="batchName" label="批次名称" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.batchName || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="任务数量" width="120" align="center">
              <template slot-scope="scope">
                <el-tag type="info">{{ scope.row.total || 0 }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="进度" width="200">
              <template slot-scope="scope">
                <el-progress :percentage="scope.row.progress || 0" :status="getProgressStatus(scope.row)"></el-progress>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100" align="center">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row)">{{ getStatusText(scope.row) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" @click="handleProcess(scope.row)" :disabled="!canProcess(scope.row)">开始处理</el-button>
                <el-button type="text" @click="viewDetails(scope.row.batchNo)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { importExcel, processTask, getAllBatches, getTaskProgress, getQueueStatus } from '@/api/task'

export default {
  name: 'TaskManagement',
  data() {
    return {
      loading: false,
      uploading: false,
      queueLoading: false,
      batches: [],
      fileList: [],
      uploadForm: {
        file: null,
        batchName: ''
      },
      uploadRules: {
        file: [
          { required: true, message: '请选择Excel文件', trigger: 'change' }
        ],
        batchName: [
          { max: 50, message: '批次名称不能超过50个字符', trigger: 'blur' }
        ]
      },
      excelColumns: [
        { name: 'proment', description: '发送给豆包的提问内容', required: true },
        { name: 'photoname', description: '关联的图片名称（对应图片管理中上传的图片名称）', required: false },
        { name: 'filename', description: '生成的Word文档文件名', required: true }
      ],
      // 轮询任务进度的定时器
      progressTimer: null,
      // 队列状态定时器
      queueTimer: null,
      // 队列状态
      queueStatus: {
        isProcessing: false,
        queueSize: 0,
        queuedBatches: []
      }
    }
  },
  created() {
    this.fetchBatches()
    this.fetchQueueStatus()
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }
    if (this.queueTimer) {
      clearInterval(this.queueTimer)
    }
  },
  methods: {
    // 获取所有批次
    async fetchBatches() {
      this.loading = true
      try {
        const res = await getAllBatches()
        this.batches = []
        
        if (res.data && res.data.length > 0) {
          // 获取每个批次的详细信息
          const promises = res.data.map(batchNo => this.fetchBatchProgress(batchNo))
          await Promise.all(promises)
        }
      } catch (error) {
        console.error('获取批次列表失败', error)
      } finally {
        this.loading = false
      }
      
      // 开始定时轮询进度
      this.startProgressPolling()
    },
    
    // 获取批次进度
    async fetchBatchProgress(batchNo) {
      try {
        const res = await getTaskProgress(batchNo)
        if (res.data) {
          // 查找是否已经存在该批次
          const index = this.batches.findIndex(b => b.batchNo === batchNo)
          if (index > -1) {
            // 更新已存在的批次
            this.$set(this.batches, index, res.data)
          } else {
            // 添加新批次
            this.batches.push(res.data)
          }
        }
      } catch (error) {
        console.error(`获取批次 ${batchNo} 进度失败`, error)
      }
    },
    
    // 开始轮询进度
    startProgressPolling() {
      // 先清除可能存在的定时器
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
      }
      
      // 设置新的定时器，每5秒轮询一次
      this.progressTimer = setInterval(() => {
        if (this.batches.length > 0) {
          // 只轮询状态不是完成或失败的批次
          const incompleteBatches = this.batches.filter(b => 
            b.progress < 100 && !(b.completed + b.failed === b.total && b.total > 0)
          )
          
          if (incompleteBatches.length > 0) {
            incompleteBatches.forEach(batch => {
              this.fetchBatchProgress(batch.batchNo)
            })
          } else {
            // 如果所有批次都完成了，停止轮询
            clearInterval(this.progressTimer)
            this.progressTimer = null
          }
        }
      }, 5000)
    },
    
    // 文件上传前校验
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                      file.type === 'application/vnd.ms-excel'
      const isLt10M = file.size / 1024 / 1024 < 10
      
      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
        return false
      }
      
      return true
    },
    
    // 文件列表变化
    handleFileChange(file, fileList) {
      if (file.status === 'ready') {
        this.uploadForm.file = file.raw
        this.fileList = fileList
      }
    },
    
    // 移除文件
    handleFileRemove() {
      this.uploadForm.file = null
      this.fileList = []
    },
    
    // 自定义上传
    handleUpload(options) {
      // 这个方法不会被调用，我们使用submitUpload来手动上传
    },
    
    // 提交上传
    submitUpload() {
      this.$refs.uploadForm.validate(async (valid) => {
        if (valid) {
          if (!this.uploadForm.file) {
            this.$message.error('请选择Excel文件')
            return
          }
          
          this.uploading = true
          try {
            const formData = new FormData()
            formData.append('file', this.uploadForm.file)
            formData.append('batchName', this.uploadForm.batchName || '')
            
            const res = await importExcel(formData)
            this.$message.success('导入成功')
            this.handleFileRemove() // 清空文件列表
            this.uploadForm.batchName = '' // 清空批次名称
            
            // 获取最新的批次列表
            await this.fetchBatches()
            
            // 询问是否立即处理
            if (res.data) {
              this.$confirm('Excel导入成功，是否立即开始处理任务?', '提示', {
                confirmButtonText: '立即处理',
                cancelButtonText: '稍后处理',
                type: 'info'
              }).then(() => {
                this.handleProcess({ batchNo: res.data })
              }).catch(() => {
                // 稍后处理，不做操作
              })
            }
          } catch (error) {
            console.error('导入失败', error)
          } finally {
            this.uploading = false
          }
        } else {
          return false
        }
      })
    },
    
    // 开始处理任务
    async handleProcess(batch) {
      try {
        await processTask(batch.batchNo)
        this.$message.success('任务处理已启动')
        
        // 更新该批次的状态
        await this.fetchBatchProgress(batch.batchNo)
        
        // 确保轮询正在进行
        this.startProgressPolling()
      } catch (error) {
        console.error('启动任务处理失败', error)
      }
    },
    
    // 查看任务详情
    viewDetails(batchNo) {
      this.$router.push(`/task-detail/${batchNo}`)
    },
    
    // 下载Excel模板
    downloadTemplate() {
      // 这里可以提供一个下载模板的功能，但需要后端支持
      // 暂时使用一个简单的警告提示
      this.$message({
        message: 'Excel模板需包含以下列：proment, photoname, filename',
        type: 'warning',
        duration: 5000
      })
    },
    
    // 获取进度状态
    getProgressStatus(batch) {
      if (batch.failed > 0) {
        return 'exception'
      }
      if (batch.progress === 100) {
        return 'success'
      }
      return ''
    },
    
    // 获取状态文本
    getStatusText(batch) {
      if (batch.total === 0) {
        return '未知'
      }
      if (batch.progress === 100) {
        return '已完成'
      }
      if (batch.processing > 0) {
        return '处理中'
      }
      if (batch.pending === batch.total) {
        return '待处理'
      }
      return '处理中'
    },
    
    // 获取状态类型
    getStatusType(batch) {
      if (batch.total === 0) {
        return 'info'
      }
      if (batch.progress === 100) {
        return 'success'
      }
      if (batch.processing > 0) {
        return 'warning'
      }
      if (batch.pending === batch.total) {
        return 'info'
      }
      return 'warning'
    },
    
    // 判断是否可以处理
    canProcess(batch) {
      return batch.pending > 0 && batch.processing === 0
    },

    // 获取队列状态
    async fetchQueueStatus() {
      this.queueLoading = true
      try {
        const res = await getQueueStatus()
        if (res.data) {
          this.queueStatus = res.data
        }
      } catch (error) {
        console.error('获取队列状态失败', error)
      } finally {
        this.queueLoading = false
      }
      
      // 开始定时轮询队列状态
      this.startQueuePolling()
    },
    
    // 开始轮询队列状态
    startQueuePolling() {
      // 先清除可能存在的定时器
      if (this.queueTimer) {
        clearInterval(this.queueTimer)
      }
      
      // 设置新的定时器，每3秒轮询一次
      this.queueTimer = setInterval(() => {
        this.fetchQueueStatus()
      }, 3000)
    }
  }
}
</script>

<style scoped>
.task-management {
  margin: 0 auto;
}
.import-form {
  max-width: 600px;
  margin-bottom: 30px;
}
.excel-uploader {
  width: 100%;
}
.format-description {
  margin-bottom: 30px;
}
.el-divider {
  margin: 24px 0;
}
.queue-status {
  margin-bottom: 30px;
}
.queue-list {
  margin-top: 10px;
}
</style> 