package com.example.dobao.service;

import com.example.dobao.entity.TaskInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 任务服务接口
 */
public interface TaskService {
    
    /**
     * 批量保存任务
     * @param taskInfoList 任务列表
     * @return 是否成功
     */
    boolean batchSaveTasks(List<TaskInfo> taskInfoList);
    
    /**
     * 从Excel文件导入任务
     * @param file Excel文件
     * @param batchName 批次名称（可选）
     * @return 批次号
     */
    String importTasksFromExcel(MultipartFile file, String batchName);
    
    /**
     * 开始处理某批次的任务
     * @param batchNo 批次号
     */
    void startProcessTasks(String batchNo);
    
    /**
     * 查询批次处理进度
     * @param batchNo 批次号
     * @return 进度信息
     */
    Map<String, Object> getProcessProgress(String batchNo);
    
    /**
     * 查询批次任务列表
     * @param batchNo 批次号
     * @return 任务列表
     */
    List<TaskInfo> getTasksByBatchNo(String batchNo);
    
    /**
     * 查询所有批次
     * @return 批次列表
     */
    List<String> getAllBatchNumbers();
    
    /**
     * 下载Word文件
     * @param id 任务ID
     * @return Word文件路径
     */
    String getWordFilePath(Long id);
    
    /**
     * 根据ID查询任务
     * @param id 任务ID
     * @return 任务信息
     */
    TaskInfo getTaskById(Long id);
    
    /**
     * 标记Word文件已删除
     * @param id 任务ID
     * @return 是否成功
     */
    boolean markWordFileDeleted(Long id);
    
    /**
     * 物理删除Word文件和数据库记录
     * @param id 任务ID
     * @return 是否成功
     */
    boolean physicalDeleteTask(Long id);
} 