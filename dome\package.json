{"name": "dome", "version": "1.0.0", "description": "Vue2基础项目框架", "main": "index.js", "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production", "serve": "webpack serve"}, "keywords": ["vue2", "element-ui", "vuex", "vue-router", "axios"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "element-ui": "^2.15.14", "vue": "^2.7.16", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.1", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "html-webpack-plugin": "^5.6.3", "style-loader": "^4.0.0", "vue-loader": "^15.11.1", "vue-template-compiler": "^2.7.16", "webpack": "^5.99.7", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}