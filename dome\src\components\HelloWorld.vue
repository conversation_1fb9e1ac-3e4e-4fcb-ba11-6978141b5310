<template>
  <div class="hello-world">
    <h1>{{ msg }}</h1>
    <el-button type="primary" @click="onClick">点击我</el-button>
  </div>
</template>

<script>
export default {
  name: 'HelloWorld',
  props: {
    msg: {
      type: String,
      default: 'Hello Vue2'
    }
  },
  methods: {
    onClick() {
      this.$message.success('Hello Vue2!')
    }
  }
}
</script>

<style scoped>
.hello-world {
  text-align: center;
  margin: 20px 0;
}
h1 {
  color: #42b983;
}
</style> 