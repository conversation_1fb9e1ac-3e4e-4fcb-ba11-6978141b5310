package com.example.dobao.service.impl;

import com.example.dobao.entity.ImageInfo;
import com.example.dobao.entity.SealLineRelation;
import com.example.dobao.service.ImageService;
import com.example.dobao.service.WordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTDrawing;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;

/**
 * Word文档服务实现
 */
@Service
@Slf4j
public class WordServiceImpl implements WordService {

    @Value("${upload.word.path:./upload/words}")
    private String wordPath;
    
    @Value("${upload.image.path:./upload/images}")
    private String imagePath;
    
    @Resource
    private ImageService imageService;
    
    // 图片名缓存：用于存储图片名称与文件名的映射关系
    private static final Map<String, String> imageNameCache = new HashMap<>();

    @Override
    public String generateWordDocument(String content, String fileName) {
        return generateWordDocumentWithImage(content, fileName, null);
    }
    
    @Override
    public String generateWordDocumentWithImage(String content, String fileName, String photoName) {
        try {
            log.info("开始生成Word文档，文件名：{}，图片名：{}", fileName, photoName);
            
            // 检查内容是否为空
            if (content == null || content.trim().isEmpty()) {
                log.warn("生成Word文档内容为空，使用默认内容替代");
                content = "API返回内容为空，请检查请求参数和API响应。";
            }
            
            // 文件名安全检查和处理
            fileName = sanitizeFileName(fileName);
            log.info("安全处理后的文件名：{}", fileName);
            
            // 创建Word文档目录
            File dir = new File(wordPath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                log.info("创建Word文档目录结果: {}, 路径: {}", created, dir.getAbsolutePath());
                if (!created) {
                    // 尝试使用绝对路径
                    dir = new File(new File(wordPath).getAbsolutePath());
                    created = dir.mkdirs();
                    log.info("使用绝对路径创建目录结果: {}, 路径: {}", created, dir.getAbsolutePath());
                    
                    // 如果仍然创建失败，尝试使用临时目录
                    if (!created) {
                        dir = new File(System.getProperty("java.io.tmpdir"), "dobao/words");
                        created = dir.mkdirs();
                        log.info("使用临时目录创建结果: {}, 路径: {}", created, dir.getAbsolutePath());
                        if (!created && !dir.exists()) {
                            throw new RuntimeException("无法创建Word文档目录: " + dir.getAbsolutePath());
                        }
                    }
                }
            }

            // 确保文件名有.docx后缀
            if (!fileName.toLowerCase().endsWith(".docx")) {
                fileName = fileName + ".docx";
            }
            
            // 检查文件名是否已存在，如果存在则添加时间戳
            File outputFile = new File(dir, fileName);
            if (outputFile.exists()) {
                String baseName = fileName.substring(0, fileName.lastIndexOf("."));
                String extension = fileName.substring(fileName.lastIndexOf("."));
                fileName = baseName + "_" + System.currentTimeMillis() + extension;
                outputFile = new File(dir, fileName);
                log.info("文件已存在，使用新文件名：{}", fileName);
            }

            log.info("开始创建Word文档对象");
            // 创建文档
            XWPFDocument document = new XWPFDocument();

            // 创建标题段落
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setFontFamily("等线");
            titleRun.setFontSize(16);
            titleRun.setBold(true);
            titleRun.setText(fileName.replace(".docx", ""));
            
            // 查找图片信息和关联的密封线图片
            ImageInfo imageInfo = null;
            File imageFile = null;
            
            if (photoName != null && !photoName.trim().isEmpty()) {
                    // 根据图片名称查找实际的图片文件
                imageFile = findImageByName(photoName);
                    
                    if (imageFile != null && imageFile.exists() && imageFile.isFile()) {
                        log.info("找到图片文件: {}", imageFile.getAbsolutePath());
                        
                    // 查找图片信息
                    imageInfo = imageService.getImageByName(photoName);
                }
            }
            
            // 如果找到图片信息，先插入密封线图片（在左侧）
            if (imageInfo != null) {
                insertSealLineImage(document, imageInfo);
            }
            
            // 再插入正常图片（居中）
            if (imageFile != null && imageFile.exists() && imageFile.isFile()) {
                try {
                    // 创建图片段落，在标题和可能的密封线图片之后
                        XWPFParagraph imageParagraph = document.createParagraph();
                        imageParagraph.setAlignment(ParagraphAlignment.CENTER);
                        // 设置段落上下间距较小
                        imageParagraph.setSpacingBefore(0);  // 100缇 = 约5磅
                        imageParagraph.setSpacingAfter(0);   // 100缇 = 约5磅
                        
                        XWPFRun imageRun = imageParagraph.createRun();
                        
                        FileInputStream fis = new FileInputStream(imageFile);
                        
                        // 获取图片原始尺寸
                        BufferedImage bufferedImage = ImageIO.read(imageFile);
                        int originalWidth = bufferedImage.getWidth();
                        int originalHeight = bufferedImage.getHeight();
                        
                        // 计算EMU单位的尺寸，保持原始宽高比
                        // 最大宽度约为页面宽度的80%
                        int maxWidthEMU = Units.toEMU(400); // 约为页面的80%宽度
                        double scale = 1.0;
                        int widthEMU = Units.pixelToEMU(originalWidth);
                        int heightEMU = Units.pixelToEMU(originalHeight);
                        
                        // 如果图片太宽，按比例缩小
                        if (widthEMU > maxWidthEMU) {
                            scale = (double)maxWidthEMU / widthEMU;
                            widthEMU = maxWidthEMU;
                            heightEMU = (int)(heightEMU * scale);
                        }
                        
                        log.info("图片原始尺寸: {}x{} 像素, 显示尺寸: {}x{} EMU", 
                                originalWidth, originalHeight, widthEMU, heightEMU);
                                
                        imageRun.addPicture(
                            fis,
                            determineImageType(imageFile.getName()),
                            imageFile.getName(),
                            widthEMU,
                            heightEMU
                        );
                        fis.close();
                        // 删除这个换行，避免图片后有太多空行
                        log.info("图片已插入Word文档");
                } catch (Exception e) {
                    log.error("插入图片失败: {}", e.getMessage(), e);
                    // 继续生成文档，但不包含图片
                }
            }

            // 创建内容段落
            XWPFParagraph paragraph = document.createParagraph();
            paragraph.setAlignment(ParagraphAlignment.LEFT);
            // 设置段落上间距较小，确保与上面的内容有适当间距但不会太大
            paragraph.setSpacingBefore(0);  // 100缇 = 约5磅

            // 分段处理内容，避免过长文本可能导致的问题
            String[] contentLines = content.split("\n");
            
            // 获取标准化文件名（不含扩展名）用于比较
            String baseFileName = fileName.replace(".docx", "").trim();
            
            // 标记是否已经遇到第一个一级标题
            boolean foundFirstBigTitle = false;
            
            for (String line : contentLines) {
                // 去掉前后空白
                String trimmedLine = line.trim();
                
                // 跳过空行
                if (trimmedLine.isEmpty()) {
                    // 完全去掉所有空行
                    continue;
                }
                
                // 跳过试卷标题行（包含学校名、课程名和学年信息）
                if (trimmedLine.contains("信息工程大学") || 
                    trimmedLine.contains("GNSS原理与应用") || 
                    trimmedLine.matches(".*\\d{4}-\\d{4}年.*[学期|试卷].*")) {
                    log.info("跳过试卷标题行: {}", trimmedLine);
                    continue;
                }
                
                // 跳过字体说明行
                if (trimmedLine.contains("字体") || 
                    trimmedLine.contains("微软雅黑") || 
                    trimmedLine.contains("宋体") || 
                    trimmedLine.contains("加粗") ||
                    trimmedLine.contains("段落间距") ||
                    trimmedLine.matches("（.*[字体|加粗|居中|间距].*）")) {
                    log.info("跳过字体说明行: {}", trimmedLine);
                    continue;
                }
                
                // 跳过考试时间信息行
                if (trimmedLine.contains("考试时间") || trimmedLine.contains("满分")) {
                    log.info("跳过考试信息行: {}", trimmedLine);
                    continue;
                }
                
                // 跳过分隔线
                if (trimmedLine.matches("^[-*_]{3,}$")) {
                    log.info("跳过分隔线: {}", trimmedLine);
                    continue;
                }
                
                // 判断是否是一级标题（如：一、单项选择题）
                boolean isBigTitle = trimmedLine.matches("^[一二三四五六七八九十]+、.*题.*");
                
                // 处理Markdown格式的标题行或一级标题
                if (trimmedLine.startsWith("#") || isBigTitle) {
                    String titleText;
                    
                    if (trimmedLine.startsWith("#")) {
                        // 从Markdown标题中提取标题文本(去掉#和前后空格)
                        titleText = trimmedLine.replaceFirst("^#+\\s*", "").trim();
                        
                        // 检查标题文本是否与文件名相似（包含关系）
                        if (titleText.contains(baseFileName) || baseFileName.contains(titleText)) {
                            log.info("跳过与文件名相似的重复标题: {}", trimmedLine);
                            continue;
                        }
                        
                        // 或者标题文本包含"南京信息工程大学"等特定文本
                        if (titleText.contains("南京信息工程大学") || 
                            titleText.contains("GNSS原理与应用") || 
                            titleText.contains("期末试卷")) {
                            log.info("跳过包含特定关键词的重复标题: {}", trimmedLine);
                            continue;
                        }
                        
                        // 判断是否是一级标题（如：一、单项选择题）
                        isBigTitle = titleText.matches("^[一二三四五六七八九十]+、.*题.*");
                    } else {
                        titleText = trimmedLine;
                    }
                    
                    // 对于非第一个一级标题，先添加一个换行
                    if (isBigTitle) {
                        if (foundFirstBigTitle) {
                            // 如果已经遇到过第一个一级标题，后续的一级标题前添加一个空行
                            XWPFRun emptyRun = paragraph.createRun();
                            emptyRun.addBreak();
                        } else {
                            // 标记已找到第一个一级标题
                            foundFirstBigTitle = true;
                        }
                    }
                    
                    // 添加粗体格式
                    XWPFRun titleRun2 = paragraph.createRun();
                    titleRun2.setFontFamily("等线");
                    titleRun2.setFontSize(12);
                    titleRun2.setBold(true);
                    titleRun2.setText(titleText);
                    titleRun2.addBreak();
                    
                    continue;
                }
                
                // 去掉文本中的Markdown格式标记**
                line = line.replaceAll("\\*\\*(.+?)\\*\\*", "$1");
                
                // 创建运行
                XWPFRun run = paragraph.createRun();
                run.setFontFamily("等线");
                run.setFontSize(11);
                run.setText(line);
                run.addBreak();  // 添加换行但不添加空行
            }

            // 保存文档
            log.info("准备将Word文档保存到：{}", outputFile.getPath());
            try (FileOutputStream out = new FileOutputStream(outputFile)) {
                document.write(out);
                out.flush();
            }

            // 验证文件是否成功创建
            if (!outputFile.exists() || outputFile.length() == 0) {
                throw new IOException("Word文档创建失败或文件大小为0");
            }
            
            log.info("生成Word文档成功，文件大小: {} 字节, 路径: {}", outputFile.length(), outputFile.getPath());
            return outputFile.getPath();
        } catch (IOException e) {
            log.error("生成Word文档IO异常: {}", e.getMessage(), e);
            throw new RuntimeException("生成Word文档IO异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("生成Word文档异常: {}", e.getMessage(), e);
            throw new RuntimeException("生成Word文档异常: " + e.getMessage());
        }
    }
    
    /**
     * 插入密封线图片到文档左侧
     * @param document Word文档对象
     * @param imageInfo 原图片信息
     */
    private void insertSealLineImage(XWPFDocument document, ImageInfo imageInfo) {
        try {
            // 查询是否有关联的密封线图片
            SealLineRelation sealLineRelation = imageService.getSealLineRelation(imageInfo.getId());
            if (sealLineRelation == null) {
                log.info("图片没有关联的密封线图片: {}", imageInfo.getImageName());
                return;
            }
            
            // 获取密封线图片信息
            ImageInfo sealLineImage = imageService.getImageById(sealLineRelation.getSealLineId());
            if (sealLineImage == null) {
                log.warn("密封线图片不存在: sealLineId={}", sealLineRelation.getSealLineId());
                return;
            }
            
            // 确认密封线图片文件存在
            File sealLineFile = new File(sealLineImage.getImagePath());
            if (!sealLineFile.exists() || !sealLineFile.isFile()) {
                log.warn("密封线图片文件不存在: {}", sealLineImage.getImagePath());
                return;
            }
            
            log.info("找到关联的密封线图片: {}, 路径: {}", sealLineImage.getImageName(), sealLineImage.getImagePath());
            
            // 读取密封线图片并获取尺寸
            BufferedImage bufferedImage = ImageIO.read(sealLineFile);
            int originalWidth = bufferedImage.getWidth();
            int originalHeight = bufferedImage.getHeight();
            
            // 从配置中获取密封线参数（如果有）
            int sealLineWidthEMU = Units.pixelToEMU(originalWidth);
            int sealLineHeightEMU = Units.pixelToEMU(originalHeight);
            
            // 环绕方式，默认为上下型环绕
            String wrapStyle = "bothSides"; // 默认上下型环绕
            
            // 如果有配置信息，应用配置中的尺寸和环绕方式
            if (sealLineRelation.getConfig() != null && !sealLineRelation.getConfig().isEmpty()) {
                try {
                    // 在这里可以解析JSON配置并应用
                    // 目前简单处理，设置一个固定宽度，高度按比例缩放
                    int maxSealLineWidthEMU = Units.toEMU(80); // 设置一个较窄的宽度
                    if (sealLineWidthEMU > maxSealLineWidthEMU) {
                        double scale = (double)maxSealLineWidthEMU / sealLineWidthEMU;
                        sealLineWidthEMU = maxSealLineWidthEMU;
                        sealLineHeightEMU = (int)(sealLineHeightEMU * scale);
                    }
                    
                    // 如果配置中指定了环绕方式，则使用配置中的环绕方式
                    if (sealLineRelation.getConfig().contains("wrapStyle")) {
                        if (sealLineRelation.getConfig().contains("tight")) {
                            wrapStyle = "tight"; // 紧密型环绕
                        } else if (sealLineRelation.getConfig().contains("through")) {
                            wrapStyle = "through"; // 穿越型环绕
                        } else if (sealLineRelation.getConfig().contains("topAndBottom")) {
                            wrapStyle = "topAndBottom"; // 上下型环绕
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析密封线配置失败: {}", e.getMessage());
                }
            }
            
            log.info("密封线图片尺寸: {}x{} 像素, 显示尺寸: {}x{} EMU, 环绕方式: {}", 
                    originalWidth, originalHeight, sealLineWidthEMU, sealLineHeightEMU, wrapStyle);

            // 读取图片数据
            byte[] imageData = Files.readAllBytes(sealLineFile.toPath());
            int imageType = determineImageType(sealLineFile.getName());
            
            // 创建段落
            XWPFParagraph paragraph = document.createParagraph();
            paragraph.setAlignment(ParagraphAlignment.LEFT);
            
            // 通过底层XML插入图片并设置为环绕文本
            insertPictureWithTextWrapping(document, paragraph, imageData, imageType, 
                    sealLineFile.getName(), sealLineWidthEMU, sealLineHeightEMU, wrapStyle);
            
            log.info("密封线图片已插入Word文档左侧（使用环绕文本方式）");
        } catch (Exception e) {
            log.error("插入密封线图片失败: {}", e.getMessage(), e);
            // 继续生成文档，不插入密封线图片
        }
    }
    
    /**
     * 插入图片并设置文本环绕方式
     * 使用底层XML操作来设置图片为环绕方式
     * @param wrapStyle 环绕样式："bothSides"(上下型), "tight"(紧密型), "through"(穿越型), "topAndBottom"(上下型)
     */
    private void insertPictureWithTextWrapping(XWPFDocument document, XWPFParagraph paragraph, 
            byte[] pictureData, int pictureType, String pictureName, 
            int width, int height, String wrapStyle) throws Exception {
        
        try {
            log.info("开始设置图片环绕格式: {}, 图片尺寸: {}x{}", wrapStyle, width, height);
            
            // 先使用标准方法添加图片到文档中
            String relationId = document.addPictureData(pictureData, pictureType);
            int id = document.getNextPicNameNumber(pictureType);
            log.debug("添加图片数据成功，关系ID: {}, 图片ID: {}", relationId, id);
            
            // 根据环绕样式选择正确的环绕元素
            String wrapElement;
            switch (wrapStyle) {
                case "tight":
                    wrapElement = "<wp:wrapTight wrapText=\"bothSides\"/>";
                    break;
                case "through":
                    wrapElement = "<wp:wrapThrough wrapText=\"bothSides\"/>";
                    break;
                case "topAndBottom":
                    wrapElement = "<wp:wrapTopAndBottom/>";
                    break;
                case "bothSides":
                default:
                    wrapElement = "<wp:wrapSquare wrapText=\"bothSides\"/>";
                    break;
            }
            log.debug("选择环绕方式: {}, XML元素: {}", wrapStyle, wrapElement);
            
            // 创建包含环绕格式的XML，设置为绝对位置，强制靠左且垂直居中
            String anchorXML = 
                    "<wp:anchor xmlns:wp=\"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing\" " +
                    "xmlns:a=\"http://schemas.openxmlformats.org/drawingml/2006/main\" " +
                    "xmlns:pic=\"http://schemas.openxmlformats.org/drawingml/2006/picture\" " +
                    "xmlns:r=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships\" " +
                    "xmlns:a14=\"http://schemas.microsoft.com/office/drawing/2010/main\" " +
                    "distT=\"0\" distB=\"0\" distL=\"0\" distR=\"0\" simplePos=\"0\" relativeHeight=\"0\" " +
                    "behindDoc=\"0\" locked=\"0\" layoutInCell=\"1\" allowOverlap=\"1\">" +
                    "<wp:simplePos x=\"0\" y=\"0\"/>" +
                    "<wp:positionH relativeFrom=\"page\">" + // 相对于页面而非列或段落
                    "<wp:posOffset>0</wp:posOffset>" + // 绝对位置偏移为0
                    "</wp:positionH>" +
                    "<wp:positionV relativeFrom=\"page\">" + // 相对于页面而非段落
                    "<wp:align>center</wp:align>" + // 垂直居中
                    "</wp:positionV>" +
                    "<wp:extent cx=\"" + width + "\" cy=\"" + height + "\"/>" +
                    "<wp:effectExtent l=\"0\" t=\"0\" r=\"0\" b=\"0\"/>" +
                    wrapElement +  // 插入选择的环绕方式
                    "<wp:docPr id=\"" + id + "\" name=\"Picture " + id + "\"/>" +
                    "<wp:cNvGraphicFramePr>" +
                    "<a:graphicFrameLocks noChangeAspect=\"1\"/>" +
                    "</wp:cNvGraphicFramePr>" +
                    "<a:graphic>" +
                    "<a:graphicData uri=\"http://schemas.openxmlformats.org/drawingml/2006/picture\">" +
                    "<pic:pic>" +
                    "<pic:nvPicPr>" +
                    "<pic:cNvPr id=\"" + id + "\" name=\"Picture " + id + "\"/>" +
                    "<pic:cNvPicPr/>" +
                    "</pic:nvPicPr>" +
                    "<pic:blipFill>" +
                    "<a:blip r:embed=\"" + relationId + "\">" +
                    "<a:extLst><a:ext uri=\"{28A0092B-C50C-407E-A947-70E740481C1C}\">" +
                    "<a14:useLocalDpi val=\"0\"/>" +
                    "</a:ext></a:extLst></a:blip>" +
                    "<a:stretch><a:fillRect/></a:stretch>" +
                    "</pic:blipFill>" +
                    "<pic:spPr>" +
                    "<a:xfrm><a:off x=\"0\" y=\"0\"/><a:ext cx=\"" + width + "\" cy=\"" + height + "\"/></a:xfrm>" +
                    "<a:prstGeom prst=\"rect\"><a:avLst/></a:prstGeom>" +
                    "</pic:spPr>" +
                    "</pic:pic>" +
                    "</a:graphicData>" +
                    "</a:graphic>" +
                    "</wp:anchor>";
            
            try {
                // 尝试先使用段落缩进设置
                paragraph.setIndentationLeft(0);  // 强制段落左缩进为0
                
                // 使用兼容性更好的方式添加图形
                // 1. 获取段落的CTP
                CTP ctp = paragraph.getCTP();
                
                // 2. 创建一个新的CTR (run)
                CTR ctr = ctp.addNewR();
                
                // 3. 在CTR中添加drawing
                CTDrawing drawing = CTDrawing.Factory.parse(anchorXML);
                ctr.setDrawingArray(new CTDrawing[]{drawing});
                
                log.info("成功设置图片为{}环绕，并强制靠左垂直居中", wrapStyle);
            } catch (Exception e) {
                log.error("XML解析或设置失败，尝试使用备用方法: {}", e.getMessage());
                
//                // 尝试使用备用方法 - 创建一个没有缩进的段落
//                XWPFParagraph newPara = document.createParagraph();
//                newPara.setAlignment(ParagraphAlignment.LEFT);
//                newPara.setIndentationLeft(0);
//                // 强制段落左边距为0
//                if (newPara.getCTP().getPPr() == null) {
//                    newPara.getCTP().addNewPPr();
//                }
//                if (newPara.getCTP().getPPr().getInd() == null) {
//                    newPara.getCTP().getPPr().addNewInd();
//                }
//                newPara.getCTP().getPPr().getInd().setLeft(org.openxmlformats.schemas.wordprocessingml.x2006.main.STTwipsMeasure.Factory.newValue(0));
//
//                XWPFRun run = newPara.createRun();
//                run.addPicture(new ByteArrayInputStream(pictureData), pictureType, pictureName, width, height);
                
                log.info("已使用备用方法插入图片（靠左放置）");
            }
        } catch (Exception e) {
            log.error("设置图片环绕格式失败，使用标准方法: {}", e.getMessage(), e);
            // 回退到最基本的方法，仍然尝试靠左放置
            XWPFParagraph newPara = document.createParagraph();
            newPara.setAlignment(ParagraphAlignment.LEFT);
            newPara.setIndentationLeft(0);
            XWPFRun run = newPara.createRun();
            run.addPicture(new ByteArrayInputStream(pictureData), pictureType, pictureName, width, height);
            log.info("已使用标准方法插入图片（靠左放置）");
        }
    }
    
    /**
     * 插入图片并设置文本环绕方式
     * 使用底层XML操作来设置图片为上下型环绕
     */
    private void insertPictureWithTextWrapping(XWPFDocument document, XWPFParagraph paragraph, 
            byte[] pictureData, int pictureType, String pictureName, 
            int width, int height) throws Exception {
        // 调用带环绕样式参数的方法，默认使用上下型环绕
        insertPictureWithTextWrapping(document, paragraph, pictureData, pictureType, 
                pictureName, width, height, "bothSides");
    }
    
    /**
     * 根据图片名称查找对应的图片文件
     * @param imageName 图片名称（如"小狗"、"南京信息工程大学"等）
     * @return 图片文件，如果找不到则返回null
     */
    private File findImageByName(String imageName) {
        if (imageName == null || imageName.trim().isEmpty()) {
            return null;
        }
        
        // 首先从数据库中查询图片信息
        ImageInfo imageInfo = imageService.getImageByName(imageName);
        if (imageInfo != null && imageInfo.getImagePath() != null) {
            File imageFile = new File(imageInfo.getImagePath());
            if (imageFile.exists() && imageFile.isFile()) {
                log.info("从数据库中找到图片: {}, 路径: {}", imageName, imageInfo.getImagePath());
                return imageFile;
            } else {
                log.warn("数据库中的图片文件不存在: {}", imageInfo.getImagePath());
            }
        }
        
        // 如果数据库中没有找到，尝试从缓存中查找
        if (imageNameCache.containsKey(imageName)) {
            String cachedFileName = imageNameCache.get(imageName);
            File cachedFile = new File(imagePath, cachedFileName);
            if (cachedFile.exists()) {
                return cachedFile;
            }
        }
        
        // 扫描图片目录
        File imageDir = new File(imagePath);
        if (!imageDir.exists() || !imageDir.isDirectory()) {
            log.warn("图片目录不存在: {}", imagePath);
            return null;
        }
        
        // 检查目录中的所有文件
        File[] files = imageDir.listFiles();
        if (files == null || files.length == 0) {
            log.warn("图片目录为空: {}", imagePath);
            return null;
        }
        
        log.info("开始在目录{}中查找图片: {}", imagePath, imageName);
        
        // 1. 首先尝试直接查找精确匹配的文件名（不带扩展名）
        for (File file : files) {
            if (file.isFile()) {
                String fileName = file.getName();
                String nameWithoutExt = fileName;
                if (fileName.contains(".")) {
                    nameWithoutExt = fileName.substring(0, fileName.lastIndexOf("."));
                }
                
                if (nameWithoutExt.equals(imageName)) {
                    log.info("找到完全匹配的图片文件: {}", fileName);
                    imageNameCache.put(imageName, fileName);
                    return file;
                }
            }
        }
        
        // 2. 尝试查找文件名中包含图片名称的文件
        for (File file : files) {
            if (file.isFile()) {
                String fileName = file.getName();
                if (fileName.contains(imageName)) {
                    log.info("找到部分匹配的图片文件: {}", fileName);
                    imageNameCache.put(imageName, fileName);
                    return file;
                }
            }
        }
        
        // 3. 作为最后手段，查找图片名称的一部分是否与文件名匹配
        for (File file : files) {
            if (file.isFile()) {
                String fileName = file.getName();
                String nameWithoutExt = fileName;
                if (fileName.contains(".")) {
                    nameWithoutExt = fileName.substring(0, fileName.lastIndexOf("."));
                }
                
                // 如果图片名称是多个字/词，尝试匹配任一部分
                String[] parts = imageName.split("[ _-]");
                for (String part : parts) {
                    if (part.length() > 1 && nameWithoutExt.contains(part)) {
                        log.info("找到部分关键词匹配的图片文件: {}, 匹配关键词: {}", fileName, part);
                        imageNameCache.put(imageName, fileName);
                        return file;
                    }
                }
            }
        }
        
        // 如果通过文件名未找到匹配，提示所有可用的文件
        log.warn("未找到匹配图片: {}，可用的图片文件有: {}", 
                imageName, 
                Arrays.toString(files));
                
        return null;
    }
    
    /**
     * 根据文件扩展名确定图片类型
     */
    private int determineImageType(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "png":
                return XWPFDocument.PICTURE_TYPE_PNG;
            case "jpg":
            case "jpeg":
                return XWPFDocument.PICTURE_TYPE_JPEG;
            case "gif":
                return XWPFDocument.PICTURE_TYPE_GIF;
            case "bmp":
                return XWPFDocument.PICTURE_TYPE_BMP;
            default:
                return XWPFDocument.PICTURE_TYPE_JPEG; // 默认JPEG
        }
    }
    
    /**
     * 文件名安全处理
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            // 如果文件名为空，生成一个随机文件名
            return "document_" + UUID.randomUUID().toString().replace("-", "");
        }
        
        // 替换文件名中的非法字符
        String sanitized = fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
        
        // 如果文件名过长，截取一部分
        if (sanitized.length() > 100) {
            sanitized = sanitized.substring(0, 100);
        }
        
        return sanitized;
    }
} 