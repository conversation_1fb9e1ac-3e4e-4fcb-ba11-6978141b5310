-- 创建数据库
CREATE DATABASE IF NOT EXISTS dobao DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE dobao;

-- 图片信息表
CREATE TABLE IF NOT EXISTS `image_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `image_name` varchar(100) NOT NULL COMMENT '图片名称',
  `image_path` varchar(255) NOT NULL COMMENT '图片路径',
  `image_type` varchar(20) NOT NULL COMMENT '图片类型',
  `image_size` bigint(20) NOT NULL COMMENT '图片大小',
  `is_seal_line` tinyint(1) DEFAULT 0 COMMENT '是否为密封线图片：0-否，1-是',
  `seal_line_config` text DEFAULT NULL COMMENT '密封线配置信息（JSON格式）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_image_name` (`image_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片信息表';

-- 密封线关联表
CREATE TABLE IF NOT EXISTS `seal_line_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `image_id` bigint(20) NOT NULL COMMENT '原图片ID',
  `seal_line_id` bigint(20) NOT NULL COMMENT '密封线图片ID',
  `config` text DEFAULT NULL COMMENT '密封线配置信息（JSON格式）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_image_id` (`image_id`),
  KEY `idx_seal_line_id` (`seal_line_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='密封线关联表';

-- 任务信息表
CREATE TABLE IF NOT EXISTS `task_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `prompt` text NOT NULL COMMENT '提示词',
  `photo_name` varchar(100) DEFAULT NULL COMMENT '图片名称',
  `file_name` varchar(100) NOT NULL COMMENT '文件名称',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '处理状态：0-未处理，1-处理中，2-处理完成，3-处理失败',
  `content` text DEFAULT NULL COMMENT '生成的内容',
  `word_path` varchar(255) DEFAULT NULL COMMENT 'Word文件路径',
  `batch_no` varchar(32) NOT NULL COMMENT '批次号',
  `batch_name` varchar(50) DEFAULT NULL COMMENT '批次名称',
  `error_msg` text DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务信息表'; 