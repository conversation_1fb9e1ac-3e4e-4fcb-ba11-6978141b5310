package com.example.dobao.service;

/**
 * Word文档服务接口
 */
public interface WordService {
    
    /**
     * 生成Word文档
     * @param content 文档内容
     * @param fileName 文件名
     * @return 文件路径
     */
    String generateWordDocument(String content, String fileName);
    
    /**
     * 生成带图片的Word文档
     * @param content 内容
     * @param fileName 文件名
     * @param photoName 图片名称
     * @return 生成的Word文档路径
     */
    String generateWordDocumentWithImage(String content, String fileName, String photoName);
} 