import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    count: 0,
    user: null
  },
  getters: {
    isLoggedIn: state => !!state.user
  },
  mutations: {
    increment(state) {
      state.count++
    },
    decrement(state) {
      state.count--
    },
    setUser(state, user) {
      state.user = user
    },
    clearUser(state) {
      state.user = null
    }
  },
  actions: {
    login({ commit }, userData) {
      // 可以在这里处理异步登录逻辑
      return new Promise((resolve) => {
        setTimeout(() => {
          commit('setUser', userData)
          resolve(userData)
        }, 1000)
      })
    },
    logout({ commit }) {
      commit('clearUser')
    }
  },
  modules: {
    // 这里可以添加其他模块
  }
}) 