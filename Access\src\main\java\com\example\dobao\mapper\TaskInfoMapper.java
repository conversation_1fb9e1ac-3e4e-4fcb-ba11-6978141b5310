package com.example.dobao.mapper;

import com.example.dobao.entity.TaskInfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 任务信息Mapper
 */
@Mapper
public interface TaskInfoMapper {

    /**
     * 批量保存任务信息
     */
    @Insert("<script>" +
            "INSERT INTO task_info(prompt, photo_name, file_name, status, batch_no, batch_name, create_time, update_time) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.prompt}, #{item.photoName}, #{item.fileName}, #{item.status}, #{item.batchNo}, #{item.batchName}, NOW(), NOW())" +
            "</foreach>" +
            "</script>")
    int batchSave(List<TaskInfo> taskInfoList);

    /**
     * 保存单个任务信息
     */
    @Insert("INSERT INTO task_info(prompt, photo_name, file_name, status, batch_no, batch_name, create_time, update_time) " +
            "VALUES(#{prompt}, #{photoName}, #{fileName}, #{status}, #{batchNo}, #{batchName}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int save(TaskInfo taskInfo);

    /**
     * 根据ID查询任务信息
     */
    @Select("SELECT * FROM task_info WHERE id = #{id}")
    TaskInfo findById(Long id);

    /**
     * 根据批次号查询任务信息
     */
    @Select("SELECT * FROM task_info WHERE batch_no = #{batchNo}")
    List<TaskInfo> findByBatchNo(String batchNo);

    /**
     * 查询所有任务信息
     */
    @Select("SELECT * FROM task_info ORDER BY create_time DESC")
    List<TaskInfo> findAll();

    /**
     * 更新任务状态
     */
    @Update("UPDATE task_info SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新任务处理结果
     */
    @Update("UPDATE task_info SET content = #{content}, word_path = #{wordPath}, " +
            "status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateResult(@Param("id") Long id, @Param("content") String content, 
                     @Param("wordPath") String wordPath, @Param("status") Integer status);

    /**
     * 更新任务错误信息
     */
    @Update("UPDATE task_info SET error_msg = #{errorMsg}, status = 3, update_time = NOW() WHERE id = #{id}")
    int updateError(@Param("id") Long id, @Param("errorMsg") String errorMsg);

    /**
     * 根据状态查询任务数量
     */
    @Select("SELECT COUNT(*) FROM task_info WHERE batch_no = #{batchNo} AND status = #{status}")
    int countByStatus(@Param("batchNo") String batchNo, @Param("status") Integer status);

    /**
     * 查询批次总任务数
     */
    @Select("SELECT COUNT(*) FROM task_info WHERE batch_no = #{batchNo}")
    int countByBatchNo(String batchNo);

    /**
     * 更新任务Word文件路径
     */
    @Update("UPDATE task_info SET word_path = #{wordPath}, update_time = NOW() WHERE id = #{id}")
    int updateWordPath(@Param("id") Long id, @Param("wordPath") String wordPath);

    /**
     * 根据ID物理删除任务记录
     */
    @Delete("DELETE FROM task_info WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
} 