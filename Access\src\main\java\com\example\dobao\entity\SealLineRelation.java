package com.example.dobao.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 密封线关联实体类
 */
@Data
@NoArgsConstructor
public class SealLineRelation {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 原图片ID
     */
    private Long imageId;

    /**
     * 密封线图片ID
     */
    private Long sealLineId;

    /**
     * 密封线配置信息（JSON格式）
     */
    private String config;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 