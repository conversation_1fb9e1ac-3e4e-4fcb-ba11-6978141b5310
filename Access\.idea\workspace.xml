<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="24adbfd7-7f63-4b47-93ad-6cd788d826b3" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\slot\apache-maven-3.5.3" />
        <option name="localRepository" value="D:\slot\apache-maven-3.5.3\sese" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\slot\apache-maven-3.5.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2wi2okGwICRIVll2i5LaThuoBOZ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.DoubaoApiTest.executor&quot;: &quot;Run&quot;,
    &quot;Application.SimpleApiTest.executor&quot;: &quot;Run&quot;,
    &quot;Application.sas.executor&quot;: &quot;Run&quot;,
    &quot;Maven.dobao [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.dobao [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.DobaoApplication (1).executor&quot;: &quot;Run&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/admin/5-6/Access/upload/images&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;configurable.group.language&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\admin\5-6\Access\upload\images" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.example.dobao.controller" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.DobaoApplication (1)">
    <configuration name="DoubaoApiTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.example.dobao.test.DoubaoApiTest" />
      <module name="easylive" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.dobao.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SimpleApiTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.example.dobao.test.SimpleApiTest" />
      <module name="easylive" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.dobao.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="sas" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.example.dobao.test.sas" />
      <module name="easylive" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.dobao.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DobaoApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="easylive" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.dobao.DobaoApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.dobao.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DobaoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="easylive" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.its.DobaoApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.DobaoApplication (1)" />
        <item itemvalue="Application.sas" />
        <item itemvalue="Application.SimpleApiTest" />
        <item itemvalue="Application.DoubaoApiTest" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="24adbfd7-7f63-4b47-93ad-6cd788d826b3" name="Changes" comment="" />
      <created>1746510250674</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746510250674</updated>
      <workItem from="1746510251750" duration="5541000" />
      <workItem from="1746534513150" duration="13957000" />
      <workItem from="1746615233425" duration="25510000" />
      <workItem from="1747146977054" duration="73000" />
      <workItem from="1747147177866" duration="870000" />
      <workItem from="1747156445291" duration="3032000" />
      <workItem from="1747212026572" duration="598000" />
      <workItem from="1747412946035" duration="567000" />
      <workItem from="1747413587351" duration="8740000" />
      <workItem from="1747572089853" duration="87000" />
      <workItem from="1747822730970" duration="4765000" />
      <workItem from="1747879405237" duration="1059000" />
      <workItem from="1747883528527" duration="4088000" />
      <workItem from="1747985660192" duration="1244000" />
      <workItem from="1747986949937" duration="20000" />
      <workItem from="1747995478808" duration="1043000" />
      <workItem from="1748011273916" duration="1526000" />
      <workItem from="1748152838437" duration="2259000" />
      <workItem from="1748264137660" duration="1037000" />
      <workItem from="1748494022895" duration="2590000" />
      <workItem from="1748512498705" duration="617000" />
      <workItem from="1748536224050" duration="4715000" />
      <workItem from="1748541793876" duration="5748000" />
      <workItem from="1748709454562" duration="6262000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="java.net.UnknownHostException" package="java.net" />
          <option name="timeStamp" value="1" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>