package com.example.dobao.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.example.dobao.entity.TaskInfo;
import com.example.dobao.service.TaskService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Excel解析监听器
 */
@Slf4j
public class TaskDataListener extends AnalysisEventListener<TaskInfo> {

    /**
     * 批量处理的数量
     */
    private static final int BATCH_COUNT = 100;

    /**
     * 数据列表
     */
    private List<TaskInfo> dataList = new ArrayList<>();

    /**
     * 任务服务
     */
    private TaskService taskService;

    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 批次名称
     */
    private String batchName;

    public TaskDataListener(TaskService taskService) {
        this.taskService = taskService;
        this.batchNo = UUID.randomUUID().toString().replace("-", "");
        // 设置默认批次名称
        this.batchName = "批次_" + new Date().getTime();
    }
    
    public TaskDataListener(TaskService taskService, String batchName) {
        this.taskService = taskService;
        this.batchNo = UUID.randomUUID().toString().replace("-", "");
        // 如果批次名称为空，则使用默认批次名称
        this.batchName = (batchName == null || batchName.trim().isEmpty()) ? 
                "批次_" + new Date().getTime() : batchName.trim();
    }

    @Override
    public void invoke(TaskInfo taskInfo, AnalysisContext analysisContext) {
        // 设置未处理状态和批次号
        taskInfo.setStatus(0);
        taskInfo.setBatchNo(batchNo);
        taskInfo.setBatchName(batchName);
        
        // 添加到数据列表
        dataList.add(taskInfo);
        
        // 达到BATCH_COUNT，需要批量处理一次数据，防止数据几万条时内存溢出
        if (dataList.size() >= BATCH_COUNT) {
            saveData();
            // 清理数据列表，方便GC回收
            dataList.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 确保最后遗留的数据也被处理
        saveData();
        log.info("所有数据解析完成！");
    }

    /**
     * 保存数据
     */
    private void saveData() {
        if (!dataList.isEmpty()) {
            log.info("{}条数据，开始保存数据库！", dataList.size());
            taskService.batchSaveTasks(dataList);
            log.info("存储数据库成功！");
        }
    }

    /**
     * 获取批次号
     */
    public String getBatchNo() {
        return batchNo;
    }
    
    /**
     * 获取批次名称
     */
    public String getBatchName() {
        return batchName;
    }
} 