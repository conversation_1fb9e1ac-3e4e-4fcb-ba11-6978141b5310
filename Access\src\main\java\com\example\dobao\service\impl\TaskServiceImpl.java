package com.example.dobao.service.impl;

import com.alibaba.excel.EasyExcel;
import com.example.dobao.entity.TaskInfo;
import com.example.dobao.excel.TaskDataListener;
import com.example.dobao.mapper.TaskInfoMapper;
import com.example.dobao.service.DoubaoApiService;
import com.example.dobao.service.ImageService;
import com.example.dobao.service.TaskService;
import com.example.dobao.service.WordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务服务实现
 */
@Service
@Slf4j
public class TaskServiceImpl implements TaskService {

    @javax.annotation.Resource
    private TaskInfoMapper taskInfoMapper;

    @javax.annotation.Resource
    private DoubaoApiService doubaoApiService;

    @javax.annotation.Resource
    private ImageService imageService;

    @javax.annotation.Resource
    private WordService wordService;

    @Override
    public boolean batchSaveTasks(List<TaskInfo> taskInfoList) {
        if (taskInfoList == null || taskInfoList.isEmpty()) {
            return false;
        }
        return taskInfoMapper.batchSave(taskInfoList) > 0;
    }

    @Override
    public String importTasksFromExcel(MultipartFile file, String batchName) {
        try {
            log.info("开始导入Excel文件：{}, 批次名称: {}", file.getOriginalFilename(), batchName);
            TaskDataListener listener = new TaskDataListener(this, batchName);
            EasyExcel.read(file.getInputStream(), TaskInfo.class, listener).sheet().doRead();
            log.info("Excel文件导入成功，批次号：{}，批次名称：{}", listener.getBatchNo(), listener.getBatchName());
            return listener.getBatchNo();
        } catch (IOException e) {
            log.error("导入Excel文件失败：{}", e.getMessage(), e);
            throw new RuntimeException("导入Excel文件失败: " + e.getMessage());
        }
    }

    @Override
    @Async("taskExecutor")
    public void startProcessTasks(String batchNo) {
        log.info("开始处理批次[{}]的任务", batchNo);
        List<TaskInfo> taskInfoList = taskInfoMapper.findByBatchNo(batchNo);
        
        if (taskInfoList.isEmpty()) {
            log.warn("批次[{}]没有任务需要处理", batchNo);
            return;
        }
        
        log.info("批次[{}]共有[{}]个任务需要处理", batchNo, taskInfoList.size());
        
        // 更新任务状态为处理中
        for (TaskInfo taskInfo : taskInfoList) {
            taskInfoMapper.updateStatus(taskInfo.getId(), 1);
        }
        
        // 使用并行流处理任务
        taskInfoList.parallelStream().forEach(this::processTask);
        
        log.info("批次[{}]的任务处理完成", batchNo);
    }

    /**
     * 处理单个任务
     */
    private void processTask(TaskInfo taskInfo) {
        try {
            log.info("开始处理任务[{}]，提示词：{}", taskInfo.getId(), taskInfo.getPrompt());
            
            // 移除不需要的图片Base64编码部分，直接调用豆包API只发送文本
            String content;
            try {
                log.info("任务[{}]开始调用豆包API", taskInfo.getId());
                // 只传递提示词，不传图片
                content = doubaoApiService.callDoubaoApi(taskInfo.getPrompt(), null);
                
                // 检查返回内容是否为错误信息
                if (StringUtils.isEmpty(content)) {
                    log.error("任务[{}]豆包API返回内容为空", taskInfo.getId());
                    taskInfoMapper.updateError(taskInfo.getId(), "豆包API返回内容为空");
                    return;
                } else if (content.startsWith("豆包API") || 
                           content.startsWith("API返回") || 
                           content.contains("HTML页面") || 
                           content.contains("无法解析为JSON")) {
                    // 这是一个错误消息，而不是正常的内容，将其作为错误处理
                    log.error("任务[{}]豆包API返回错误信息: {}", taskInfo.getId(), content);
                    taskInfoMapper.updateError(taskInfo.getId(), content);
                    return;
                }
                
                log.info("任务[{}]豆包API调用成功", taskInfo.getId());
            } catch (Exception e) {
                log.error("任务[{}]调用豆包API失败：{}", taskInfo.getId(), e.getMessage(), e);
                taskInfoMapper.updateError(taskInfo.getId(), "调用豆包API失败: " + e.getMessage());
                return;
            }
            
            // 生成Word文档
            String wordPath;
            try {
                log.info("任务[{}]开始生成Word文档", taskInfo.getId());
                // 使用带图片的Word生成方法，传递photoName参数用于查找图片
                wordPath = wordService.generateWordDocumentWithImage(content, taskInfo.getFileName(), taskInfo.getPhotoName());
                log.info("任务[{}]Word文档生成成功：{}", taskInfo.getId(), wordPath);
            } catch (Exception e) {
                log.error("任务[{}]生成Word文档失败：{}", taskInfo.getId(), e.getMessage(), e);
                // 将API返回内容保存到数据库，但将状态标记为失败
                taskInfoMapper.updateResult(taskInfo.getId(), content, null, 3);
                taskInfoMapper.updateError(taskInfo.getId(), "生成Word文档失败: " + e.getMessage());
                return;
            }
            
            // 更新任务状态
            taskInfoMapper.updateResult(taskInfo.getId(), content, wordPath, 2);
            
            log.info("任务[{}]处理完成", taskInfo.getId());
        } catch (Exception e) {
            log.error("任务[{}]处理过程中发生未预期的异常：{}", taskInfo.getId(), e.getMessage(), e);
            taskInfoMapper.updateError(taskInfo.getId(), "处理过程中发生异常: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessProgress(String batchNo) {
        Map<String, Object> result = new HashMap<>();
        
        int total = taskInfoMapper.countByBatchNo(batchNo);
        int pending = taskInfoMapper.countByStatus(batchNo, 0);
        int processing = taskInfoMapper.countByStatus(batchNo, 1);
        int completed = taskInfoMapper.countByStatus(batchNo, 2);
        int failed = taskInfoMapper.countByStatus(batchNo, 3);
        
        // 查询批次名称
        String batchName = null;
        List<TaskInfo> tasks = taskInfoMapper.findByBatchNo(batchNo);
        if (!tasks.isEmpty()) {
            batchName = tasks.get(0).getBatchName();
        }
        
        result.put("batchNo", batchNo);
        result.put("batchName", batchName);
        result.put("total", total);
        result.put("pending", pending);
        result.put("processing", processing);
        result.put("completed", completed);
        result.put("failed", failed);
        result.put("progress", total == 0 ? 0 : (completed + failed) * 100 / total);
        
        return result;
    }

    @Override
    public List<TaskInfo> getTasksByBatchNo(String batchNo) {
        return taskInfoMapper.findByBatchNo(batchNo);
    }

    @Override
    public List<String> getAllBatchNumbers() {
        // 从所有任务中提取不重复的批次号
        List<TaskInfo> allTasks = taskInfoMapper.findAll();
        return allTasks.stream()
                .map(TaskInfo::getBatchNo)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public String getWordFilePath(Long id) {
        TaskInfo taskInfo = taskInfoMapper.findById(id);
        if (taskInfo != null && taskInfo.getStatus() == 2) {
            return taskInfo.getWordPath();
        }
        return null;
    }

    @Override
    public TaskInfo getTaskById(Long id) {
        return taskInfoMapper.findById(id);
    }

    @Override
    public boolean markWordFileDeleted(Long id) {
        try {
            // 获取任务信息
            TaskInfo taskInfo = taskInfoMapper.findById(id);
            if (taskInfo == null) {
                log.warn("任务[{}]不存在，无法标记Word文件已删除", id);
                return false;
            }
            
            // 更新任务Word路径为null，表示文件已删除
            // 但保留其他信息，如内容和状态
            taskInfoMapper.updateWordPath(id, null);
            
            log.info("任务[{}]的Word文件已标记为删除", id);
            return true;
        } catch (Exception e) {
            log.error("标记任务[{}]Word文件已删除时发生错误: {}", id, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean physicalDeleteTask(Long id) {
        try {
            // 获取任务信息
            TaskInfo taskInfo = taskInfoMapper.findById(id);
            if (taskInfo == null) {
                log.warn("任务[{}]不存在，无法物理删除", id);
                return false;
            }
            
            // 删除Word文件（如果存在）
            String wordPath = taskInfo.getWordPath();
            if (wordPath != null && !wordPath.isEmpty()) {
                File file = new File(wordPath);
                if (file.exists() && file.isFile()) {
                    boolean deleted = file.delete();
                    if (!deleted) {
                        log.warn("无法删除文件: {}", wordPath);
                        // 继续执行，仍然删除数据库记录
                    } else {
                        log.info("成功删除文件: {}", wordPath);
                    }
                }
            }
            
            // 从数据库中删除记录
            int result = taskInfoMapper.deleteById(id);
            
            if (result > 0) {
                log.info("任务[{}]已从数据库中物理删除", id);
                return true;
            } else {
                log.warn("删除任务[{}]数据库记录失败", id);
                return false;
            }
        } catch (Exception e) {
            log.error("物理删除任务[{}]时发生错误: {}", id, e.getMessage(), e);
            return false;
        }
    }
} 