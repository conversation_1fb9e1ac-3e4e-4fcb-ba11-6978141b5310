# Vue2 基础项目框架

## 项目说明
这是一个基于 Vue2 的基础项目框架，集成了以下技术栈：
- Vue2
- Vuex
- Vue Router
- Axios
- Element UI 2

## 目录结构
```
├── public               # 静态资源目录
│   └── index.html       # HTML 模板
├── src                  # 源代码
│   ├── api              # API 请求
│   ├── assets           # 静态资源
│   ├── components       # 公共组件
│   ├── router           # 路由配置
│   ├── store            # Vuex 状态管理
│   ├── views            # 页面
│   ├── App.vue          # 根组件
│   └── main.js          # 入口文件
├── .babelrc             # Babel 配置
├── package.json         # 包管理文件
├── README.md            # 项目说明
└── webpack.config.js    # Webpack 配置
```

## 安装依赖
```bash
npm install
```

## 启动开发服务器
```bash
npm run dev
```

## 构建生产版本
```bash
npm run build
```

## 特性
- 集成 Element UI 2 组件库
- Vuex 状态管理
- Vue Router 路由管理
- Axios 请求封装
- Webpack 5 构建 