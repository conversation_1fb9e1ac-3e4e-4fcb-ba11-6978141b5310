<template>
  <div class="home">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="welcome-card">
          <div slot="header">
            <h2>欢迎使用豆包API批量调用系统</h2>
          </div>
          <div class="card-content">
            <p>本系统支持批量调用豆包大模型API，一次处理多个任务，高效完成内容生成。</p>
            <div class="feature-list">
              <h3>主要功能：</h3>
              <ul>
                <li>
                  <i class="el-icon-picture"></i>
                  <div>
                    <h4>图片管理</h4>
                    <p>上传、管理和删除图片，支持PNG和JPG格式</p>
                  </div>
                </li>
                <li>
                  <i class="el-icon-s-order"></i>
                  <div>
                    <h4>Excel批量导入</h4>
                    <p>支持Excel文件批量导入任务，一次处理多个请求</p>
                  </div>
                </li>
                <li>
                  <i class="el-icon-s-cooperation"></i>
                  <div>
                    <h4>多线程处理</h4>
                    <p>使用多线程技术并行处理任务，提高效率</p>
                  </div>
                </li>
                <li>
                  <i class="el-icon-download"></i>
                  <div>
                    <h4>Word文档生成</h4>
                    <p>自动将API返回结果生成格式统一的Word文档</p>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

<!--    <el-row :gutter="20" style="margin-top: 20px;">-->
<!--      <el-col :span="8">-->
<!--        <el-card class="quick-action-card">-->
<!--          <div slot="header">-->
<!--            <h3>图片管理</h3>-->
<!--          </div>-->
<!--          <div class="card-content">-->
<!--            <p>管理系统中的图片资源，用于与文本一起发送给豆包API</p>-->
<!--            <el-button type="primary" @click="$router.push('/image-management')">进入图片管理</el-button>-->
<!--          </div>-->
<!--        </el-card>-->
<!--      </el-col>-->
<!--      <el-col :span="8">-->
<!--        <el-card class="quick-action-card">-->
<!--          <div slot="header">-->
<!--            <h3>任务管理</h3>-->
<!--          </div>-->
<!--          <div class="card-content">-->
<!--            <p>导入Excel文件，批量创建和管理任务</p>-->
<!--            <el-button type="primary" @click="$router.push('/task-management')">进入任务管理</el-button>-->
<!--          </div>-->
<!--        </el-card>-->
<!--      </el-col>-->
<!--      <el-col :span="8">-->
<!--        <el-card class="quick-action-card">-->
<!--          <div slot="header">-->
<!--            <h3>任务进度</h3>-->
<!--          </div>-->
<!--          <div class="card-content">-->
<!--            <p>查看所有任务的执行进度和处理结果</p>-->
<!--            <el-button type="primary" @click="$router.push('/task-progress')">查看任务进度</el-button>-->
<!--          </div>-->
<!--        </el-card>-->
<!--      </el-col>-->
<!--    </el-row>-->
  </div>
</template>

<script>
export default {
  name: 'Home'
}
</script>

<style scoped>
.home {
  margin: 0 auto;
}
.welcome-card {
  margin-bottom: 20px;
}
.card-content {
  line-height: 1.6;
}
.feature-list ul {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 20px 0;
}
.feature-list li {
  display: flex;
  width: 50%;
  padding: 15px;
  box-sizing: border-box;
}
.feature-list i {
  font-size: 24px;
  color: #409EFF;
  margin-right: 10px;
  margin-top: 5px;
}
.feature-list h4 {
  margin: 0 0 5px 0;
  color: #303133;
}
.feature-list p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
.quick-action-card {
  height: 200px;
}
.quick-action-card .card-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100px;
}
</style> 