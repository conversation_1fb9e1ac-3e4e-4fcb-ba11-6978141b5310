import request from './config'

// 上传图片
export function uploadImage(data) {
  return request({
    url: '/api/images/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取所有图片
export function getAllImages() {
  return request({
    url: '/api/images/list',
    method: 'get'
  })
}

// 根据ID获取图片
export function getImageById(id) {
  return request({
    url: `/api/images/${id}`,
    method: 'get'
  })
}

// 删除图片
export function deleteImage(id) {
  return request({
    url: `/api/images/${id}`,
    method: 'delete'
  })
} 

// 更新图片信息
export function updateImage(id, data) {
  const config = {};
  
  // 检查是否为FormData类型
  if (data instanceof FormData) {
    config.headers = {
      'Content-Type': 'multipart/form-data'
    };
  }
  
  return request({
    url: `/api/images/${id}`,
    method: 'put',
    data,
    ...config
  })
}

// 上传密封线图片
export function uploadSealLineImage(data) {
  return request({
    url: '/api/images/seal-line/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 关联密封线图片
export function associateSealLine(imageId, sealLineId, config) {
  return request({
    url: `/api/images/${imageId}/seal-line/${sealLineId}`,
    method: 'post',
    params: { config }
  })
}

// 获取图片的密封线关联
export function getSealLineRelation(imageId) {
  return request({
    url: `/api/images/${imageId}/seal-line`,
    method: 'get'
  })
}

// 删除图片的密封线关联
export function removeSealLineRelation(imageId) {
  return request({
    url: `/api/images/${imageId}/seal-line`,
    method: 'delete'
  })
} 