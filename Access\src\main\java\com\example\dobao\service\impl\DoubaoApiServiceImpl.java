package com.example.dobao.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.example.dobao.config.DoubaoApiConfig;
import com.example.dobao.service.DoubaoApiService;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 豆包API服务实现
 */
@Service
@Slf4j
public class DoubaoApiServiceImpl implements DoubaoApiService {

    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 3;
    
    // API URL
    private static final String BASE_URL = "https://ark.cn-beijing.volces.com/api/v3";
    
    // 可用的模型列表
    private static final List<String> AVAILABLE_MODELS = new ArrayList<>();
    
    static {
        // 添加可用的模型 - 根据错误提示更新模型列表
        AVAILABLE_MODELS.add("doubao-1-5-thinking-pro-250415"); // 优先使用这个模型
        AVAILABLE_MODELS.add("doubao-pro");
        AVAILABLE_MODELS.add("doubao-lite");
        // 移除不可用的模型
        // AVAILABLE_MODELS.add("doubao-lite-128k");
    }

    @javax.annotation.Resource
    private DoubaoApiConfig doubaoApiConfig;

    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(180, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build();

    @Override
    public String callDoubaoApi(String prompt, String imageBase64) {
        // 默认使用已知可用的模型
        String modelName = "doubao-1-5-thinking-pro-250415";
        
        // 如果配置中的模型在可用列表中，则使用配置的模型
        String configModel = StringUtils.isEmpty(imageBase64) ? 
                doubaoApiConfig.getThinkingModel() : doubaoApiConfig.getVisionModel();
        if (AVAILABLE_MODELS.contains(configModel)) {
            modelName = configModel;
        } else {
            log.warn("配置的模型 {} 不在已知的可用模型列表中，将使用默认模型 {}", configModel, modelName);
        }
        
        // 重试计数器
        int retryCount = 0;
        Exception lastException = null;
        
        while (retryCount <= MAX_RETRY_COUNT) {
            try {
                if (retryCount > 0) {
                    // 指数退避策略，等待时间随重试次数增加
                    long waitTime = (long) Math.pow(2, retryCount) * 1000;
                    log.info("第{}次重试调用豆包API，等待{}毫秒后开始", retryCount, waitTime);
                    Thread.sleep(waitTime);
                }
                
                // 初始化Ark服务
                ArkService arkService = createArkService();
                
                // 创建消息列表
                final List<ChatMessage> messages = createChatMessages(prompt, imageBase64);
                
                // 构建请求
                ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                        .model(modelName)
                        .messages(messages)
                        .temperature(0.7)
                        .maxTokens(2000)
                        .build();
                
                if (retryCount > 0) {
                    log.info("第{}次重试豆包API请求参数: {}", retryCount, JSON.toJSONString(chatCompletionRequest));
                } else {
                    log.info("豆包API请求参数: {}", JSON.toJSONString(chatCompletionRequest));
                }
                
                log.info("开始发送请求到豆包API: {}", BASE_URL);
                
                try {
                    // 发送请求并获取结果
                    String content = arkService.createChatCompletion(chatCompletionRequest)
                            .getChoices()
                            .get(0)
                            .getMessage()
                            .getContent()
                            .toString();
                    
                    // 记录成功响应
                    log.info("豆包API解析得到内容: {}", content.substring(0, Math.min(100, content.length())) + (content.length() > 100 ? "..." : ""));
                    
                    // 关闭服务
                    arkService.shutdownExecutor();
                    
                    return content;
                } catch (Exception e) {
                    // 关闭服务
                    arkService.shutdownExecutor();
                    
                    log.error("调用火山引擎SDK发生异常: {}", e.getMessage(), e);
                    
                    if (e.getMessage() != null) {
                    // 检查是否为模型未开通的错误
                        if (e.getMessage().contains("ModelNotOpen")) {
                        log.error("您的账户未开通模型 {}，请在火山引擎控制台激活该模型服务", modelName);
                        return "豆包API错误: 您的账户未开通模型 " + modelName + "，请在火山引擎控制台激活该模型服务。可以尝试使用其他模型如doubao-lite。";
                    }
                    
                        // 检查是否是可以重试的错误
                        if (e.getMessage().contains("rate limit") || 
                            e.getMessage().contains("timeout") || 
                            e.getMessage().contains("server error") ||
                            e.getMessage().contains("busy")) {
                            lastException = e;
                            retryCount++;
                            if (retryCount <= MAX_RETRY_COUNT) {
                            continue;
                            }
                        }
                    }
                    
                    throw e;
                        }
                
            } catch (Exception e) {
                log.error("调用豆包API异常: {}", e.getMessage(), e);
                lastException = e;
                retryCount++;
                
                if (retryCount > MAX_RETRY_COUNT) {
                    throw new RuntimeException("调用豆包API异常，重试" + MAX_RETRY_COUNT + "次后仍然失败: " + e.getMessage());
                }
            }
        }
        
        // 如果所有重试都失败，抛出最后一个异常
        if (lastException != null) {
            throw new RuntimeException("调用豆包API失败，重试" + MAX_RETRY_COUNT + "次后仍然失败: " + lastException.getMessage());
        }
        
        // 不应该执行到这里，但为了避免编译错误
        return "API调用发生未知错误";
    }
    
    /**
     * 创建ArkService实例
     */
    private ArkService createArkService() {
        // 初始化连接池和分发器
        ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
        Dispatcher dispatcher = new Dispatcher();
        
        // 构建ArkService
        return ArkService.builder()
                .dispatcher(dispatcher)
                .connectionPool(connectionPool)
                .baseUrl(BASE_URL)
                .apiKey(doubaoApiConfig.getApiKey())
                .build();
    }
    
    /**
     * 创建聊天消息列表
     */
    private List<ChatMessage> createChatMessages(String prompt, String imageBase64) {
        final List<ChatMessage> messages = new ArrayList<>();
        
        if (StringUtils.isEmpty(imageBase64)) {
            // 纯文本模式
            final ChatMessage userMessage = ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content(prompt)
                    .build();
            messages.add(userMessage);
        } else {
            // 图文模式 - SDK不支持多模态，回退到使用原始API调用
            log.info("图文模式暂不支持使用SDK调用，回退到使用原始API调用");
            
            // 选择合适的模型
            String modelForImages = "doubao-1-5-thinking-pro-250415"; // 默认模型
            if (AVAILABLE_MODELS.contains(doubaoApiConfig.getVisionModel())) {
                modelForImages = doubaoApiConfig.getVisionModel();
            }
            
            // 构建请求体
            MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
            JSONObject requestBody = new JSONObject();
            // 使用默认的或者已验证可用的模型
            requestBody.put("model", modelForImages);
            
            JSONArray messagesArray = new JSONArray();
            JSONObject userMessage = new JSONObject();
            userMessage.put("role", "user");
            
            // 构建多模态内容
            JSONArray contentArray = new JSONArray();
            
            // 添加文本内容
            JSONObject textContent = new JSONObject();
            textContent.put("type", "text");
            textContent.put("text", prompt);
            contentArray.add(textContent);
            
            // 添加图片内容
            JSONObject imageContent = new JSONObject();
            imageContent.put("type", "image_url");
            
            JSONObject imageUrl = new JSONObject();
            imageUrl.put("url", "data:image/jpeg;base64," + imageBase64);
            imageContent.put("image_url", imageUrl);
            
            contentArray.add(imageContent);
        
            userMessage.put("content", contentArray);
            messagesArray.add(userMessage);
            requestBody.put("messages", messagesArray);
        
        // 设置温度和最大token
        requestBody.put("temperature", 0.7);
        requestBody.put("max_tokens", 2000);
        
            // 发送请求
            RequestBody body = RequestBody.create(requestBody.toJSONString(), mediaType);
            
            Request request = new Request.Builder()
                    .url(BASE_URL + "/chat/completions")
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "application/json")
                    .addHeader("Authorization", "Bearer " + doubaoApiConfig.getApiKey())
                    .build();
                    
            try {
                Response response = client.newCall(request).execute();
                
                if (response.isSuccessful() && response.body() != null) {
                    String responseStr = response.body().string();
                    JSONObject jsonResponse = JSON.parseObject(responseStr);
                    JSONArray choices = jsonResponse.getJSONArray("choices");
                    
                    if (choices != null && !choices.isEmpty()) {
                        JSONObject firstChoice = choices.getJSONObject(0);
                        JSONObject message = firstChoice.getJSONObject("message");
                        
                        if (message != null) {
                            String content = message.getString("content");
                            
                            // 创建一个普通的文本消息来返回结果
                            final ChatMessage textMessage = ChatMessage.builder()
                                    .role(ChatMessageRole.ASSISTANT)
                                    .content(content)
                                    .build();
                            messages.add(textMessage);
                            
                            // 提前返回，这样可以避免后面的错误
                            return messages;
                        }
                    }
                }
                
                log.error("图文API调用失败");
                
                // 创建一个包含错误信息的消息
                final ChatMessage errorMessage = ChatMessage.builder()
                        .role(ChatMessageRole.ASSISTANT)
                        .content("图文识别调用失败，请稍后再试")
                        .build();
                messages.add(errorMessage);
                
            } catch (Exception e) {
                log.error("图文API调用异常", e);
                
                // 创建一个包含错误信息的消息
                final ChatMessage errorMessage = ChatMessage.builder()
                        .role(ChatMessageRole.ASSISTANT)
                        .content("图文识别调用异常: " + e.getMessage())
                        .build();
                messages.add(errorMessage);
            }
        }
        
        return messages;
    }
} 