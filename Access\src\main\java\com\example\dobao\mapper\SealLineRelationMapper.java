package com.example.dobao.mapper;

import com.example.dobao.entity.SealLineRelation;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 密封线关联Mapper
 */
@Mapper
public interface SealLineRelationMapper {

    /**
     * 保存密封线关联
     */
    @Insert("INSERT INTO seal_line_relation(image_id, seal_line_id, config, create_time, update_time) " +
            "VALUES(#{imageId}, #{sealLineId}, #{config}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int save(SealLineRelation sealLineRelation);

    /**
     * 根据ID查询密封线关联
     */
    @Select("SELECT * FROM seal_line_relation WHERE id = #{id}")
    SealLineRelation findById(Long id);

    /**
     * 根据图片ID查询密封线关联
     */
    @Select("SELECT * FROM seal_line_relation WHERE image_id = #{imageId}")
    SealLineRelation findByImageId(Long imageId);

    /**
     * 根据密封线图片ID查询密封线关联
     */
    @Select("SELECT * FROM seal_line_relation WHERE seal_line_id = #{sealLineId}")
    List<SealLineRelation> findBySealLineId(Long sealLineId);

    /**
     * 更新密封线关联
     */
    @Update("UPDATE seal_line_relation SET seal_line_id = #{sealLineId}, config = #{config}, update_time = NOW() " +
            "WHERE id = #{id}")
    int update(SealLineRelation sealLineRelation);

    /**
     * 删除密封线关联
     */
    @Delete("DELETE FROM seal_line_relation WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 根据图片ID删除密封线关联
     */
    @Delete("DELETE FROM seal_line_relation WHERE image_id = #{imageId}")
    int deleteByImageId(Long imageId);

    /**
     * 根据密封线图片ID删除密封线关联
     */
    @Delete("DELETE FROM seal_line_relation WHERE seal_line_id = #{sealLineId}")
    int deleteBySealLineId(Long sealLineId);
} 