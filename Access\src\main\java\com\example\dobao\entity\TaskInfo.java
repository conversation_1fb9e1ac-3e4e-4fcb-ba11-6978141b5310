package com.example.dobao.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 任务信息实体类，用于Excel导入
 */
@Data
@NoArgsConstructor
public class TaskInfo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 提示词，发送给豆包的问题
     */
    @ExcelProperty("proment")
    private String prompt;

    /**
     * 图片名称，对应前端管理的图片
     */
    @ExcelProperty("photoname")
    private String photoName;

    /**
     * 生成的文件名称
     */
    @ExcelProperty("filename")
    private String fileName;

    /**
     * 处理状态：0-未处理，1-处理中，2-处理完成，3-处理失败
     */
    private Integer status;

    /**
     * 生成的内容（豆包API返回的内容）
     */
    private String content;

    /**
     * 生成的Word文件路径
     */
    private String wordPath;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 批次号，用于标识同一批次的任务
     */
    private String batchNo;

    /**
     * 批次名称，用于前端显示
     */
    private String batchName;

    /**
     * 错误信息
     */
    private String errorMsg;
} 