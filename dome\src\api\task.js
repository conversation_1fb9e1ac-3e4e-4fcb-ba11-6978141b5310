import request from './config'

// 导入Excel文件
export function importExcel(data) {
  return request({
    url: '/api/tasks/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 开始处理任务
export function processTask(batchNo) {
  return request({
    url: `/api/tasks/process/${batchNo}`,
    method: 'post'
  })
}

// 获取进度
export function getTaskProgress(batchNo) {
  return request({
    url: `/api/tasks/progress/${batchNo}`,
    method: 'get'
  })
}

// 获取批次任务列表
export function getTasksByBatchNo(batchNo) {
  return request({
    url: `/api/tasks/list/${batchNo}`,
    method: 'get'
  })
}

// 获取所有批次
export function getAllBatches() {
  return request({
    url: '/api/tasks/batches',
    method: 'get'
  })
}

// 下载Word文件
export function downloadWord(id) {
  return request({
    url: `/api/tasks/download/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 删除Word文件
export function deleteWord(id) {
  return request({
    url: `/api/tasks/deleteWord/${id}`,
    method: 'delete'
  })
}

// 物理删除Word文件及数据库记录
export function physicalDeleteWord(id) {
  return request({
    url: `/api/tasks/physicalDeleteWord/${id}`,
    method: 'delete'
  })
}

// 批量删除Word文件
export function batchDeleteWords(ids) {
  return request({
    url: '/api/tasks/batchDeleteWords',
    method: 'delete',
    data: ids
  })
}

// 批量下载Word文件（打包为ZIP）
export function batchDownloadWords(ids) {
  return request({
    url: '/api/tasks/batchDownloadWords',
    method: 'post',
    data: ids,
    responseType: 'blob'
  })
}

// 批量物理删除Word文件及数据库记录
export function batchPhysicalDeleteWords(ids) {
  return request({
    url: '/api/tasks/batchPhysicalDeleteWords',
    method: 'delete',
    data: ids
  })
} 