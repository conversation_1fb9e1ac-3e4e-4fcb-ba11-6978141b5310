import request from './config'

// 导入Excel文件
export function importExcel(data) {
  return request({
    url: '/api/tasks/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 处理任务
export function processTask(batchNo) {
  return request({
    url: `/api/tasks/process/${batchNo}`,
    method: 'post'
  })
}

// 获取处理进度
export function getTaskProgress(batchNo) {
  return request({
    url: `/api/tasks/progress/${batchNo}`,
    method: 'get'
  })
}

// 获取批次任务列表
export function getTaskList(batchNo) {
  return request({
    url: `/api/tasks/list/${batchNo}`,
    method: 'get'
  })
}

// 获取所有批次
export function getAllBatches() {
  return request({
    url: '/api/tasks/batches',
    method: 'get'
  })
}

// 下载Word文件
export function downloadWord(id) {
  return request({
    url: `/api/tasks/download/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 删除Word文件
export function deleteWord(id) {
  return request({
    url: `/api/tasks/deleteWord/${id}`,
    method: 'delete'
  })
}

// 物理删除Word文件及数据库记录
export function physicalDeleteWord(id) {
  return request({
    url: `/api/tasks/physicalDeleteWord/${id}`,
    method: 'delete'
  })
}

// 批量下载Word文件
export function batchDownloadWords(ids) {
  return request({
    url: '/api/tasks/batchDownloadWords',
    method: 'post',
    data: ids,
    responseType: 'blob'
  })
}

// 批量删除Word文件
export function batchDeleteWords(ids) {
  return request({
    url: '/api/tasks/batchDeleteWords',
    method: 'delete',
    data: ids
  })
}

// 批量物理删除Word文件及数据库记录
export function batchPhysicalDeleteWords(ids) {
  return request({
    url: '/api/tasks/batchPhysicalDeleteWords',
    method: 'delete',
    data: ids
  })
}

// 获取队列状态
export function getQueueStatus() {
  return request({
    url: '/api/tasks/queue/status',
    method: 'get'
  })
}

// 获取失败的任务列表
export function getFailedTasks() {
  return request({
    url: '/api/tasks/failed',
    method: 'get'
  })
}

// 重试单个失败任务
export function retryFailedTask(id) {
  return request({
    url: `/api/tasks/retry/${id}`,
    method: 'post'
  })
}

// 批量重试失败任务
export function retryFailedTasks(ids) {
  return request({
    url: '/api/tasks/retry/batch',
    method: 'post',
    data: ids
  })
}

// 获取处理中的任务列表
export function getProcessingTasks() {
  return request({
    url: '/api/tasks/processing',
    method: 'get'
  })
}

// 重启处理中的任务
export function restartProcessingTasks(ids) {
  return request({
    url: '/api/tasks/restart/processing',
    method: 'post',
    data: ids
  })
} 