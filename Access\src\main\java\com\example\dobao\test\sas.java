package com.example.dobao.test;

import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class sas {
    private static final String API_KEY = "5ee3c0ba-26fe-4284-a263-347e9d5577bc";
    private static final String BASE_URL = "https://ark.cn-beijing.volces.com/api/v3";
    private static final String MODEL_NAME = "doubao-1-5-thinking-pro-250415";

    public static void main(String[] args) {
        System.out.println("开始测试豆包API调用...");
        
        // 初始化连接池和分发器
        ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
        Dispatcher dispatcher = new Dispatcher();
        
        // 构建ArkService
        ArkService service = ArkService.builder()
                .dispatcher(dispatcher)
                .connectionPool(connectionPool)
                .baseUrl(BASE_URL)
                .apiKey(API_KEY)
                .build();

        try {
            System.out.println("\n----- 标准请求 -----");
            // 构建消息
            final List<ChatMessage> messages = new ArrayList<>();
            final ChatMessage systemMessage = ChatMessage.builder()
                    .role(ChatMessageRole.SYSTEM)
                    .content("你是人工智能助手.")
                    .build();
            final ChatMessage userMessage = ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content("你好")
                    .build();
            messages.add(systemMessage);
            messages.add(userMessage);

            // 构建请求
            ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                    .model(MODEL_NAME)
                    .messages(messages)
                    .build();

            System.out.println("发送请求到: " + BASE_URL);
            
            // 发送请求并获取响应
            service.createChatCompletion(chatCompletionRequest)
                    .getChoices()
                    .forEach(choice -> System.out.println("响应内容: " + choice.getMessage().getContent()));

            System.out.println("\n----- 流式请求 -----");
            // 构建流式请求
            service.streamChatCompletion(chatCompletionRequest)
                    .doOnError(e -> {
                        System.out.println("请求异常: " + e.getMessage());
                        e.printStackTrace();
                    })
                    .blockingForEach(
                            choice -> {
                                if (choice.getChoices().size() > 0) {
                                    System.out.print(choice.getChoices().get(0).getMessage().getContent());
                                }
                            }
                    );
        } catch (Exception e) {
            System.out.println("请求异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭服务
            service.shutdownExecutor();
            System.out.println("\n测试完成");
        }
    }
}