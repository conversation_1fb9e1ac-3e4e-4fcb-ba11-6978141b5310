package com.example.dobao.service;

import com.example.dobao.entity.ImageInfo;
import com.example.dobao.entity.SealLineRelation;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 图片服务接口
 */
public interface ImageService {
    
    /**
     * 上传图片
     * @param imageName 图片名称
     * @param file 图片文件
     * @return 图片信息
     */
    ImageInfo uploadImage(String imageName, MultipartFile file);
    
    /**
     * 根据ID查询图片
     * @param id 图片ID
     * @return 图片信息
     */
    ImageInfo getImageById(Long id);
    
    /**
     * 根据图片名称查询图片
     * @param imageName 图片名称
     * @return 图片信息
     */
    ImageInfo getImageByName(String imageName);
    
    /**
     * 查询所有图片
     * @return 图片列表
     */
    List<ImageInfo> getAllImages();
    
    /**
     * 删除图片
     * @param id 图片ID
     * @return 是否成功
     */
    boolean deleteImage(Long id);
    
    /**
     * 获取图片Base64编码
     * @param imageName 图片名称
     * @return Base64编码
     */
    String getImageBase64(String imageName);

    /**
     * 更新图片信息
     * @param imageInfo 图片信息
     * @return 是否成功
     */
    boolean updateImage(ImageInfo imageInfo);
    
    /**
     * 更新图片（包括图片文件）
     * @param id 图片ID
     * @param imageName 图片名称
     * @param file 图片文件
     * @return 更新后的图片信息
     */
    ImageInfo updateImageWithFile(Long id, String imageName, MultipartFile file);
    
    /**
     * 上传密封线图片
     * @param imageName 图片名称
     * @param file 图片文件
     * @param sealLineConfig 密封线配置
     * @return 图片信息
     */
    ImageInfo uploadSealLineImage(String imageName, MultipartFile file, String sealLineConfig);
    
    /**
     * 关联密封线图片
     * @param imageId 原图片ID
     * @param sealLineId 密封线图片ID
     * @param config 配置信息
     * @return 关联信息
     */
    SealLineRelation associateSealLine(Long imageId, Long sealLineId, String config);
    
    /**
     * 获取图片的密封线关联
     * @param imageId 图片ID
     * @return 密封线关联
     */
    SealLineRelation getSealLineRelation(Long imageId);
    
    /**
     * 删除密封线关联
     * @param imageId 图片ID
     * @return 是否成功
     */
    boolean removeSealLineRelation(Long imageId);
} 