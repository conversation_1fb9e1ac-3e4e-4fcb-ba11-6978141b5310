<template>
  <div class="about">
    <el-card>
      <div slot="header">
        <h2>关于</h2>
      </div>
      <div class="content">
        <p>这是一个Vue2基础项目框架，集成了以下技术：</p>
        <el-divider></el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="hover">
              <h3>Vue2</h3>
              <p>渐进式JavaScript框架</p>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <h3>Vue Router</h3>
              <p>官方路由管理器</p>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-card shadow="hover">
              <h3>Vuex</h3>
              <p>状态管理模式</p>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <h3>Element UI</h3>
              <p>桌面端组件库</p>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12" :offset="6">
            <el-card shadow="hover">
              <h3>Axios</h3>
              <p>基于Promise的HTTP客户端</p>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'About'
}
</script>

<style scoped>
.about {
  max-width: 800px;
  margin: 0 auto;
}
.content {
  text-align: center;
}
</style> 