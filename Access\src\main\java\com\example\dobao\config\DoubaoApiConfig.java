package com.example.dobao.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 豆包API配置
 */
@Configuration
public class DoubaoApiConfig {

    /**
     * API Key
     */
    @Value("${doubao.api.key:5ee3c0ba-26fe-4284-a263-347e9d5577bc}")
    private String apiKey;

    /**
     * 视觉模型
     */
    @Value("${doubao.model.vision:doubao-1-5-thinking-pro-250415}")
    private String visionModel;

    /**
     * 思考模型
     */
    @Value("${doubao.model.thinking:doubao-1-5-thinking-pro-250415}")
    private String thinkingModel;

    /**
     * 线程池大小
     */
    @Value("${doubao.thread.pool.size:5}")
    private int threadPoolSize;

    public String getApiKey() {
        return apiKey;
    }

    public String getVisionModel() {
        return visionModel;
    }

    public String getThinkingModel() {
        return thinkingModel;
    }

    public int getThreadPoolSize() {
        return threadPoolSize;
    }
} 