import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import axios from 'axios'

Vue.config.productionTip = false

// 使用ElementUI
Vue.use(ElementUI)

// 配置axios
Vue.prototype.$http = axios
// 配置请求基础URL
// axios.defaults.baseURL = 'http://api.example.com'

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app') 