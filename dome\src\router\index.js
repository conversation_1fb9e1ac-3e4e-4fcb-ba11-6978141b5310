import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

// 路由懒加载
const Home = () => import('@/views/Home.vue')
const ImageManagement = () => import('@/views/ImageManagement.vue')
const TaskManagement = () => import('@/views/TaskManagement.vue')
const TaskProgress = () => import('@/views/TaskProgress.vue')
const TaskDetail = () => import('@/views/TaskDetail.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页 - 豆包API批量调用系统'
    }
  },
  {
    path: '/image-management',
    name: 'ImageManagement',
    component: ImageManagement,
    meta: {
      title: '图片管理 - 豆包API批量调用系统'
    }
  },
  {
    path: '/task-management',
    name: 'TaskManagement',
    component: TaskManagement,
    meta: {
      title: '任务管理 - 豆包API批量调用系统'
    }
  },
  {
    path: '/task-progress',
    name: 'TaskProgress',
    component: TaskProgress,
    meta: {
      title: '任务进度 - 豆包API批量调用系统'
    }
  },
  {
    path: '/task-detail/:batchNo',
    name: 'TaskDetail',
    component: TaskDetail,
    meta: {
      title: '任务详情 - 豆包API批量调用系统'
    }
  },
  {
    path: '*',
    redirect: '/'
  }
]

const router = new VueRouter({
  mode: 'hash', // 可选 'history'
  base: '/',
  routes
})

// 路由前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || '豆包API批量调用系统'
  next()
})

export default router 